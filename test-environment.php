<?php
/**
 * Environment Test Page
 * Shows current environment detection and configuration
 */

include 'config.php';
?>
<!DOCTYPE html>
<html lang="ckb" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Environment Test - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .info-card { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 10px 0; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .code { background: #f8f9fa; border: 1px solid #e9ecef; padding: 10px; border-radius: 4px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Environment Configuration Test</h1>
        
        <div class="info-card success">
            <h3>✅ Environment Detection</h3>
            <p><strong>Current Environment:</strong> <?php echo CURRENT_ENVIRONMENT; ?></p>
            <p><strong>Site Name:</strong> <?php echo SITE_NAME; ?></p>
            <p><strong>Debug Mode:</strong> <?php echo DEBUG_MODE ? 'Enabled' : 'Disabled'; ?></p>
        </div>

        <div class="info-card info">
            <h3>🌐 URL Configuration</h3>
            <p><strong>Base URL:</strong> <?php echo BASE_URL; ?></p>
            <p><strong>Admin URL:</strong> <?php echo ADMIN_URL; ?></p>
            <p><strong>API URL:</strong> <?php echo API_URL; ?></p>
            <p><strong>Assets URL:</strong> <?php echo ASSETS_URL; ?></p>
            <p><strong>Uploads URL:</strong> <?php echo UPLOADS_URL; ?></p>
        </div>

        <div class="info-card warning">
            <h3>🖥️ Server Information</h3>
            <p><strong>HTTP Host:</strong> <?php echo $_SERVER['HTTP_HOST'] ?? 'Not set'; ?></p>
            <p><strong>Server Name:</strong> <?php echo $_SERVER['SERVER_NAME'] ?? 'Not set'; ?></p>
            <p><strong>Server Address:</strong> <?php echo $_SERVER['SERVER_ADDR'] ?? 'Not set'; ?></p>
            <p><strong>Request URI:</strong> <?php echo $_SERVER['REQUEST_URI'] ?? 'Not set'; ?></p>
        </div>

        <div class="info-card">
            <h3>🔗 Helper Function Tests</h3>
            <div class="code">
                <p><strong>url('home'):</strong> <?php echo url('home'); ?></p>
                <p><strong>admin_url('users'):</strong> <?php echo admin_url('users'); ?></p>
                <p><strong>api_url('test-handler'):</strong> <?php echo api_url('test-handler'); ?></p>
                <p><strong>assets_url('css/style.css'):</strong> <?php echo assets_url('css/style.css'); ?></p>
                <p><strong>uploads_url('profile.jpg'):</strong> <?php echo uploads_url('profile.jpg'); ?></p>
            </div>
        </div>

        <div class="info-card">
            <h3>🗄️ Database Connection Test</h3>
            <?php
            global $con;
            if ($con && !$con->connect_error) {
                echo '<p class="text-success">✅ Database connected successfully!</p>';
                
                // Test a simple query
                $result = $con->query("SELECT COUNT(*) as count FROM users");
                if ($result) {
                    $count = $result->fetch_assoc()['count'];
                    echo '<p class="text-info">📊 Users in database: ' . $count . '</p>';
                } else {
                    echo '<p class="text-warning">⚠️ Could not query users table: ' . $con->error . '</p>';
                }
            } else {
                echo '<p class="text-danger">❌ Database connection failed!</p>';
                if ($con) {
                    echo '<p class="text-danger">Error: ' . $con->connect_error . '</p>';
                }
            }
            ?>
        </div>

        <div class="info-card">
            <h3>🧪 Environment Functions Test</h3>
            <p><strong>is_development():</strong> <?php echo is_development() ? 'true' : 'false'; ?></p>
            <p><strong>is_production():</strong> <?php echo is_production() ? 'true' : 'false'; ?></p>
        </div>

        <?php if (DEBUG_MODE): ?>
        <div class="info-card">
            <h3>🐛 Debug Information</h3>
            <p>Debug mode is enabled. This section only appears in development.</p>
            <?php
            debug([
                'Environment' => CURRENT_ENVIRONMENT,
                'Base URL' => BASE_URL,
                'Debug Mode' => DEBUG_MODE,
                'Server Info' => [
                    'HTTP_HOST' => $_SERVER['HTTP_HOST'] ?? 'Not set',
                    'SERVER_NAME' => $_SERVER['SERVER_NAME'] ?? 'Not set',
                    'SERVER_ADDR' => $_SERVER['SERVER_ADDR'] ?? 'Not set'
                ]
            ], 'Complete Environment Info');
            ?>
        </div>
        <?php endif; ?>

        <div class="info-card">
            <h3>🔗 Quick Links</h3>
            <p><a href="<?php echo url('home'); ?>" class="btn btn-primary">Home Page</a></p>
            <p><a href="<?php echo url('about'); ?>" class="btn btn-info">About Page</a></p>
            <p><a href="<?php echo admin_url(); ?>" class="btn btn-warning">Admin Panel</a></p>
            <p><a href="<?php echo url('online-test'); ?>" class="btn btn-success">Online Test</a></p>
        </div>

        <div class="info-card">
            <h3>📝 Instructions</h3>
            <p><strong>For Localhost:</strong> This page should show "localhost" environment</p>
            <p><strong>For Production:</strong> This page should show "production" environment</p>
            <p><strong>Database:</strong> Should connect automatically to the correct database</p>
            <p><strong>URLs:</strong> Should generate correct URLs for the current environment</p>
            
            <div class="alert alert-warning mt-3">
                <strong>⚠️ Security Note:</strong> Delete this test file (test-environment.php) before deploying to production!
            </div>
        </div>
    </div>
</body>
</html>
