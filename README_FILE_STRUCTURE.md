# ئیلهامبەخشی دەروونی - File Structure Documentation

## 📁 New Organized File Structure

### 🏗️ Root Directory
```
inspiremental/
├── index.php                 # Main router file
├── db.php                   # Database connection
├── login.php                # Authentication pages
├── register.php
├── logout.php
├── .htaccess               # URL rewriting rules
└── manifest.json           # PWA manifest
```

### 📂 Admin Panel (`/admin/`)
```
admin/
├── index.php              # Admin dashboard
├── users.php              # User management
├── tests.php              # Test results management
├── transactions.php       # Transaction management
└── edit_user.php          # User editing form
```

### 🔌 API Endpoints (`/api/`)
```
api/
├── test-handler.php       # Test processing
├── send_email.php         # Email functionality
├── get_profile_data.php   # User profile data
├── update_profile.php     # Profile updates
├── get_schedule.php       # Schedule retrieval
├── update_schedule.php    # Schedule updates
├── get_user_schedule.php  # User-specific schedules
└── get_week_schedule.php  # Weekly schedule data
```

### 📄 Main Pages (`/pages/`)
```
pages/
├── home.php               # Homepage
├── about.php              # About page
├── contact.php            # Contact page
├── online-test.php        # Online tests
├── talk-to-me.php         # Chat interface
├── student.php            # Student portal
├── teacher.php            # Teacher portal
├── school-guidance.php    # School guidance
├── counseling.php         # Counseling services
└── find-center.php        # Center locator
```

### 🎨 Assets (`/assets/`)
```
assets/
├── css/
│   └── style.css          # Main stylesheet
└── js/
    └── script.js          # Main JavaScript
```

### 🔧 Includes (`/includes/`)
```
includes/
├── head.php               # HTML head section
├── script.php             # JavaScript includes
└── admin_navbar.php       # Admin navigation
```

### 📁 Other Directories
```
img/                       # Images and media files
uploads/                   # User uploaded files
font/                      # Custom fonts
guide/                     # Guidance content
```

## 🌐 Clean URL Structure

### Public URLs
- `https://yoursite.com/home` → Homepage
- `https://yoursite.com/about` → About page
- `https://yoursite.com/online-test` → Online tests
- `https://yoursite.com/talk-to-me` → Chat interface
- `https://yoursite.com/contact` → Contact page

### Admin URLs
- `https://yoursite.com/admin` → Admin dashboard
- `https://yoursite.com/admin/users` → User management
- `https://yoursite.com/admin/tests` → Test management
- `https://yoursite.com/admin/transactions` → Transaction management

### API URLs
- `https://yoursite.com/api/test-handler` → Test processing
- `https://yoursite.com/api/send_email` → Email sending
- `https://yoursite.com/api/get_profile_data` → Profile data

## 🔒 Security Features

### .htaccess Security
- Prevents access to `.sql` files
- Blocks access to hidden files (`.env`, `.git`, etc.)
- Sets security headers (X-Frame-Options, X-XSS-Protection)
- Proper URL routing with security checks

### File Organization Benefits
- **Separation of Concerns**: Admin, API, and public files are separated
- **Clean URLs**: SEO-friendly and user-friendly URLs
- **Security**: Sensitive files are properly organized and protected
- **Maintainability**: Easy to find and update specific functionality
- **Scalability**: Easy to add new features in appropriate directories

## 🔄 Migration Notes

### Updated References
- All API calls now use `/api/` prefix
- Admin panel uses `/admin/` prefix
- CSS/JS files moved to `/assets/` directory
- Common includes moved to `/includes/` directory

### Database Connections
- All API files updated to use `../db.php`
- Admin files use `../db.php` for database connection
- Pages use relative paths from root (via index.php inclusion)

### URL Routing
- `.htaccess` handles clean URL routing
- Admin panel has separate routing rules
- API endpoints have dedicated routing
- Security rules prevent unauthorized access

## 🚀 Benefits of New Structure

1. **Professional Organization**: Industry-standard file structure
2. **Better Security**: Proper separation and access controls
3. **Clean URLs**: SEO and user-friendly URLs
4. **Easy Maintenance**: Logical file organization
5. **Scalability**: Easy to add new features
6. **Performance**: Optimized asset loading
7. **Development**: Easier for team collaboration

## 📝 Usage Examples

### Linking to Pages
```html
<a href="about">About Us</a>
<a href="online-test">Take Test</a>
<a href="contact">Contact</a>
```

### Admin Links
```html
<a href="admin">Admin Panel</a>
<a href="admin/users">Manage Users</a>
```

### API Calls (JavaScript)
```javascript
fetch('api/get_profile_data')
fetch('api/update_profile', { method: 'POST', body: formData })
```

This new structure provides a solid foundation for the mental health platform with proper organization, security, and scalability.
