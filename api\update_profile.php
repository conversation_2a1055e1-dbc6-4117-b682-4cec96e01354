<?php
session_start();
include 'db.php';

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];
$username = $_POST['username'];
$email = $_POST['email'];
$password = !empty($_POST['password']) ? password_hash($_POST['password'], PASSWORD_BCRYPT) : null;

$profilePhotoPath = null;

if (isset($_FILES['profilePhoto']) && $_FILES['profilePhoto']['error'] === UPLOAD_ERR_OK) {
    $targetDir = 'uploads/profile_photos/';
    if (!is_dir($targetDir)) {
        mkdir($targetDir, 0755, true);
    }
    
    $fileName = basename($_FILES['profilePhoto']['name']);
    $fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);
    $uniqueFileName = $user_id . '_profile.' . $fileExtension;
    $profilePhotoPath = $targetDir . $uniqueFileName;

    // Crop and resize the image
    $uploadedImage = $_FILES['profilePhoto']['tmp_name'];
    $imageResource = imagecreatefromstring(file_get_contents($uploadedImage));

    // Get original dimensions
    $width = imagesx($imageResource);
    $height = imagesy($imageResource);

    // Determine the size of the square crop (smallest dimension)
    $side = min($width, $height);

    // Create a square cropped image
    $croppedImage = imagecrop($imageResource, [
        'x' => ($width - $side) / 2,
        'y' => ($height - $side) / 2,
        'width' => $side,
        'height' => $side
    ]);

    // Resize to 512x512
    $resizedImage = imagescale($croppedImage, 512, 512);

    // Save the resized image
    if ($fileExtension === 'jpg' || $fileExtension === 'jpeg') {
        imagejpeg($resizedImage, $profilePhotoPath);
    } elseif ($fileExtension === 'png') {
        imagepng($resizedImage, $profilePhotoPath);
    } elseif ($fileExtension === 'gif') {
        imagegif($resizedImage, $profilePhotoPath);
    }

    // Free up memory
    imagedestroy($imageResource);
    imagedestroy($croppedImage);
    imagedestroy($resizedImage);
}

if ($password && $profilePhotoPath) {
    $stmt = $con->prepare("UPDATE users SET username = ?, email = ?, password = ?, profile_photo = ? WHERE id = ?");
    $stmt->bind_param("ssssi", $username, $email, $password, $profilePhotoPath, $user_id);
} elseif ($profilePhotoPath) {
    $stmt = $con->prepare("UPDATE users SET username = ?, email = ?, profile_photo = ? WHERE id = ?");
    $stmt->bind_param("sssi", $username, $email, $profilePhotoPath, $user_id);
} elseif ($password) {
    $stmt = $con->prepare("UPDATE users SET username = ?, email = ?, password = ? WHERE id = ?");
    $stmt->bind_param("sssi", $username, $email, $password, $user_id);
} else {
    $stmt = $con->prepare("UPDATE users SET username = ?, email = ? WHERE id = ?");
    $stmt->bind_param("ssi", $username, $email, $user_id);
}

if ($stmt->execute()) {
    echo json_encode(['success' => true]);
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to update profile']);
}
?>
