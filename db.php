<?php
/**
 * Database Configuration - Auto-detects environment
 * Works for both localhost and published website
 */

// Detect environment based on server name or IP
function detectEnvironment() {
    $host = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] ?? 'localhost';
    $serverAddr = $_SERVER['SERVER_ADDR'] ?? '127.0.0.1';
    $documentRoot = $_SERVER['DOCUMENT_ROOT'] ?? '';
    $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';

    // Check if it's localhost/development environment
    if (
        strpos($host, 'localhost') !== false ||
        strpos($host, '127.0.0.1') !== false ||
        strpos($host, '::1') !== false ||
        $serverAddr === '127.0.0.1' ||
        $serverAddr === '::1' ||
        strpos($host, '.local') !== false
    ) {
        return 'localhost';
    }

    // Check if it's the published domain
    if (
        strpos($host, 'inspiremental.org') !== false ||
        strpos($host, 'yaridagr.com') !== false ||
        strpos($documentRoot, 'yaridagr') !== false ||
        strpos($scriptName, 'public_html') !== false
    ) {
        return 'production';
    }

    // Default to localhost for safety
    return 'localhost';
}

// Get current environment
$environment = detectEnvironment();

// Database configurations
$dbConfigs = [
    'localhost' => [
        'servername' => 'localhost',
        'username' => 'root',
        'password' => '',
        'dbname' => 'inspiremental'
    ],
    'production' => [
        'servername' => 'localhost',
        'username' => 'yaridagr_inspiremental',
        'password' => 'n*dMFX=i0-iq',
        'dbname' => 'yaridagr_inspiremental'
    ]
];

// Get configuration for current environment
$config = $dbConfigs[$environment];

// Extract database credentials
$servername = $config['servername'];
$username = $config['username'];
$password = $config['password'];
$dbname = $config['dbname'];

// Create connection
$con = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($con->connect_error) {
    // Log error for debugging (don't show sensitive info in production)
    error_log("Database connection failed for environment '$environment': " . $con->connect_error);

    if ($environment === 'localhost') {
        die("Database connection failed. Please check if XAMPP/MySQL is running and the database 'inspiremental' exists.");
    } else {
        die("Database connection failed. Please contact the administrator.");
    }
}

// Set character set to avoid encoding issues
$con->set_charset("utf8mb4");

// Optional: Set global environment variable for use in other files
define('CURRENT_ENVIRONMENT', $environment);

// Generate BASE_URL dynamically based on environment
if ($environment === 'localhost') {
    define('BASE_URL', 'http://localhost/inspiremental/');
} else {
    // For production, construct URL properly
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'] ?? 'inspiremental.org';

    // Clean up the host if it contains path information
    if (strpos($host, '/') !== false) {
        $host = explode('/', $host)[0];
    }

    define('BASE_URL', $protocol . $host . '/');
}
?>