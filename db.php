<?php
/**
 * Database Configuration - Auto-detects environment
 * Works for both localhost and published website
 */

// Detect environment based on server name or IP
function detectEnvironment() {
    $host = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] ?? 'localhost';
    $serverAddr = $_SERVER['SERVER_ADDR'] ?? '127.0.0.1';
    $documentRoot = $_SERVER['DOCUMENT_ROOT'] ?? '';
    $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';

    // Check if it's localhost/development environment
    if (
        strpos($host, 'localhost') !== false ||
        strpos($host, '127.0.0.1') !== false ||
        strpos($host, '::1') !== false ||
        $serverAddr === '127.0.0.1' ||
        $serverAddr === '::1' ||
        strpos($host, '.local') !== false
    ) {
        return 'localhost';
    }

    // Check if it's the published domain
    if (
        strpos($host, 'inspiremental.org') !== false ||
        strpos($host, 'yaridagr.com') !== false ||
        strpos($documentRoot, 'yaridagr') !== false ||
        strpos($scriptName, 'public_html') !== false
    ) {
        return 'production';
    }

    // Default to localhost for safety
    return 'localhost';
}

// Get current environment
$environment = detectEnvironment();

// Database configurations
$dbConfigs = [
    'localhost' => [
        'servername' => 'localhost',
        'username' => 'root',
        'password' => '',
        'dbname' => 'inspiremental'
    ],
    'production' => [
        // Multiple configurations to try for production
        [
            'servername' => 'localhost',
            'username' => 'yaridagr_inspiremental',
            'password' => 'n*dMFX=i0-iq',
            'dbname' => 'yaridagr_inspiremental'
        ],
        [
            'servername' => 'localhost',
            'username' => 'yaridagr_inspiremental',
            'password' => 'n*dMFX=i0-iq',
            'dbname' => 'inspiremental'
        ],
        [
            'servername' => 'localhost',
            'username' => 'yaridagr_inspire',
            'password' => 'n*dMFX=i0-iq',
            'dbname' => 'yaridagr_inspire'
        ]
    ]
];

// Get configuration for current environment
if ($environment === 'localhost') {
    $config = $dbConfigs[$environment];
} else {
    // For production, try multiple configurations
    $config = null;
    $lastError = '';

    foreach ($dbConfigs['production'] as $testConfig) {
        $testCon = new mysqli($testConfig['servername'], $testConfig['username'], $testConfig['password'], $testConfig['dbname']);

        if (!$testCon->connect_error) {
            $config = $testConfig;
            $testCon->close();
            break;
        } else {
            $lastError = $testCon->connect_error;
            $testCon->close();
        }
    }

    if (!$config) {
        error_log("All production database configurations failed. Last error: $lastError");
        die("Database configuration error. Please run production-db-checker.php to find the correct settings.");
    }
}

// Extract database credentials
$servername = $config['servername'];
$username = $config['username'];
$password = $config['password'];
$dbname = $config['dbname'];

// Create connection
$con = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($con->connect_error) {
    // Log error for debugging (don't show sensitive info in production)
    error_log("Database connection failed for environment '$environment': " . $con->connect_error);

    if ($environment === 'localhost') {
        $errorMsg = "
        <div style='background: #ffcccc; padding: 20px; border: 1px solid #ff0000; margin: 20px; border-radius: 5px;'>
            <h2>❌ Database Connection Failed</h2>
            <p><strong>Error:</strong> " . $con->connect_error . "</p>
            <h3>🔧 Quick Fixes:</h3>
            <ol>
                <li><strong>Start XAMPP:</strong> Make sure Apache and MySQL are running in XAMPP Control Panel</li>
                <li><strong>Check Database:</strong> <a href='http://localhost/phpmyadmin' target='_blank'>Open phpMyAdmin</a> and create 'inspiremental' database</li>
                <li><strong>Run Setup:</strong> <a href='database-diagnostic.php'>Database Diagnostic</a> | <a href='setup-database.php'>Auto Setup</a></li>
            </ol>
            <p><strong>Need help?</strong> <a href='database-diagnostic.php'>Run Database Diagnostic</a></p>
        </div>";
        die($errorMsg);
    } else {
        die("Database connection failed. Please contact the administrator.");
    }
}

// Set character set to avoid encoding issues
$con->set_charset("utf8mb4");

// Optional: Set global environment variable for use in other files
define('CURRENT_ENVIRONMENT', $environment);

// Generate BASE_URL dynamically based on environment
if ($environment === 'localhost') {
    define('BASE_URL', 'http://localhost/inspiremental/');
} else {
    // For production, construct URL properly
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'] ?? 'inspiremental.org';

    // Clean up the host if it contains path information
    if (strpos($host, '/') !== false) {
        $host = explode('/', $host)[0];
    }

    define('BASE_URL', $protocol . $host . '/');
}
?>