<?php
/**
 * Database Configuration - Auto-detects environment
 * Works for both localhost and published website
 */

// Production-only environment detection
if (!function_exists('detectEnvironment')) {
    function detectEnvironment() {
        $host = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] ?? '';
        $documentRoot = $_SERVER['DOCUMENT_ROOT'] ?? '';
        $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';

        // Only check for production environment
        if (
            strpos($host, 'inspiremental.org') !== false ||
            strpos($host, 'yaridagr.com') !== false ||
            strpos($documentRoot, 'yaridagr') !== false ||
            strpos($scriptName, 'public_html') !== false
        ) {
            return 'production';
        }

        // If not production, show error
        die('This website only works on inspiremental.org domain.');
    }
}

// Get current environment
$environment = detectEnvironment();

// Production-only database configuration
$productionConfigs = [
    [
        'servername' => 'localhost',
        'username' => 'yaridagr_inspiremental',
        'password' => 'n*dMFX=i0-iq',
        'dbname' => 'yaridagr_inspiremental'
    ]
];

// Use the production configuration
$config = $productionConfigs[0];

// Extract database credentials
$servername = $config['servername'];
$username = $config['username'];
$password = $config['password'];
$dbname = $config['dbname'];

// Create connection
$con = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($con->connect_error) {
    // Log error for debugging
    error_log("Production database connection failed: " . $con->connect_error);
    die("Database connection failed. Please contact the administrator.");
}

// Set character set to avoid encoding issues
$con->set_charset("utf8mb4");

// Set production environment constants
define('CURRENT_ENVIRONMENT', 'production');

// Generate BASE_URL for production
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
$host = $_SERVER['HTTP_HOST'] ?? 'inspiremental.org';

// Clean up the host if it contains path information
if (strpos($host, '/') !== false) {
    $host = explode('/', $host)[0];
}

define('BASE_URL', $protocol . $host . '/');
?>