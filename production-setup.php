<?php
/**
 * Production Database Setup
 * Sets up the database with correct credentials for production
 */

echo "<h1>🚀 Production Database Setup</h1>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>1. Testing Database Connection...</h2>";

// Test both database names
$test_configs = [
    'yaridagr_inspiremental' => [
        'servername' => 'localhost',
        'username' => 'yaridagr_inspiremental',
        'password' => 'n*dMFX=i0-iq',
        'dbname' => 'yaridagr_inspiremental'
    ],
    'inspiremental' => [
        'servername' => 'localhost',
        'username' => 'yaridagr_inspiremental',
        'password' => 'n*dMFX=i0-iq',
        'dbname' => 'inspiremental'
    ]
];

$working_config = null;

foreach ($test_configs as $name => $config) {
    echo "<h3>Testing database: $name</h3>";
    
    try {
        $test_con = new mysqli($config['servername'], $config['username'], $config['password'], $config['dbname']);
        
        if ($test_con->connect_error) {
            echo "<p style='color: red;'>❌ Failed: " . $test_con->connect_error . "</p>";
        } else {
            echo "<p style='color: green;'>✅ Success: Connected to $name</p>";
            
            // Check if tests table exists
            $result = $test_con->query("SHOW TABLES LIKE 'tests'");
            if ($result && $result->num_rows > 0) {
                echo "<p style='color: green;'>✅ Tests table exists</p>";
                
                // Count tests
                $count_result = $test_con->query("SELECT COUNT(*) as count FROM tests");
                if ($count_result) {
                    $count = $count_result->fetch_assoc()['count'];
                    echo "<p style='color: blue;'>📊 Tests in database: $count</p>";
                    
                    if ($count > 0) {
                        $working_config = $config;
                        $working_config['name'] = $name;
                        echo "<p style='color: green;'>🎯 This database has test data!</p>";
                    }
                }
            } else {
                echo "<p style='color: orange;'>⚠️ Tests table does not exist</p>";
            }
            
            $test_con->close();
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
}

if ($working_config) {
    echo "<div style='background: #ccffcc; padding: 20px; border: 1px solid #00ff00; margin: 20px 0;'>";
    echo "<h2>✅ Working Database Found!</h2>";
    echo "<p><strong>Database Name:</strong> " . $working_config['name'] . "</p>";
    echo "<p><strong>Username:</strong> " . $working_config['username'] . "</p>";
    echo "<p><strong>Tests Available:</strong> Yes</p>";
    echo "</div>";
    
    echo "<h2>2. Testing API with Working Database...</h2>";
    
    // Test the API
    echo "<p><a href='api/test-handler?test_id=1' target='_blank'>🧪 Test API: test_id=1</a></p>";
    echo "<p><a href='api/test-handler?test_id=6' target='_blank'>🧪 Test API: test_id=6</a></p>";
    echo "<p><a href='online-test' target='_blank'>📋 Test Online Test Page</a></p>";
    
} else {
    echo "<div style='background: #ffcccc; padding: 20px; border: 1px solid #ff0000; margin: 20px 0;'>";
    echo "<h2>❌ No Working Database Found</h2>";
    echo "<p>Neither database configuration worked. You need to:</p>";
    echo "<ol>";
    echo "<li>Check if the database exists in cPanel</li>";
    echo "<li>Verify the database user has permissions</li>";
    echo "<li>Import the database SQL file</li>";
    echo "<li>Create the tests table and data</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>3. Manual Database Setup:</h2>";
    echo "<p>If you need to create the database manually:</p>";
    echo "<ol>";
    echo "<li>Go to cPanel → MySQL Databases</li>";
    echo "<li>Create database: yaridagr_inspiremental</li>";
    echo "<li>Ensure user yaridagr_inspiremental has ALL PRIVILEGES</li>";
    echo "<li>Import your SQL file or run setup-database.php</li>";
    echo "</ol>";
}

echo "<h2>4. Current db.php Configuration:</h2>";
echo "<p>The db.php file is now configured to try both database names automatically.</p>";

echo "<h2>5. Next Steps:</h2>";
echo "<ol>";
echo "<li>Test the API links above</li>";
echo "<li>If they work, delete this setup file</li>";
echo "<li>If they don't work, check the server error logs</li>";
echo "<li>Contact hosting support if needed</li>";
echo "</ol>";

echo "<div style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; margin: 20px 0;'>";
echo "<h3>⚠️ Security Warning</h3>";
echo "<p><strong>Delete this file (production-setup.php) after testing!</strong></p>";
echo "</div>";
?>
