<?php
include 'db.php';

header('Content-Type: application/json');

$userId = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;

// Kurdish Sorani translations for days of the week
$daysOfWeekKurdish = [
    'Sunday' => 'یەک شەممە',
    'Monday' => 'دوو شەممە',
    'Tuesday' => 'سێ شەممە',
    'Wednesday' => 'چوار شەممە',
    'Thursday' => 'پێنج شەممە',
    'Friday' => 'هەینی',
    'Saturday' => 'شەممە'
];

if ($userId > 0) {
    $query = "SELECT day_of_week, start_time, end_time FROM users_schedule WHERE user_id = ? ORDER BY FIELD(day_of_week, 'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'), start_time";
    $stmt = $con->prepare($query);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    $schedule = [];
    while ($row = $result->fetch_assoc()) {
        // Convert times to 12-hour format with AM/PM
        $row['start_time'] = date("g:i A", strtotime($row['start_time']));
        $row['end_time'] = date("g:i A", strtotime($row['end_time']));

        // Translate the day of the week to Kurdish Sorani
        $dayInKurdish = $daysOfWeekKurdish[$row['day_of_week']] ?? $row['day_of_week'];

        $schedule[$dayInKurdish][] = [
            "start_time" => $row['start_time'],
            "end_time" => $row['end_time']
        ];
    }

    // Format data to send in JSON response
    $formattedSchedule = [];
    foreach ($schedule as $day => $slots) {
        $formattedSchedule[] = [
            "day_of_week" => $day,
            "slots" => $slots
        ];
    }

    echo json_encode(["success" => true, "schedule" => $formattedSchedule]);
} else {
    echo json_encode(["success" => false, "message" => "Invalid user ID."]);
}

$con->close();
