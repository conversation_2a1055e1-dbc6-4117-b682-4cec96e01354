<?php
/**
 * Production Configuration Override
 * Use this file to force production settings if auto-detection fails
 */

// Force production environment
function forceProductionEnvironment() {
    return 'production';
}

// Override the detectEnvironment function if needed
if (!function_exists('detectEnvironment')) {
    function detectEnvironment() {
        return forceProductionEnvironment();
    }
}

// Production database configurations to try
$productionDbConfigs = [
    [
        'servername' => 'localhost',
        'username' => 'yaridagr_inspiremental',
        'password' => 'n*dMFX=i0-iq',
        'dbname' => 'inspiremental'
    ],
    [
        'servername' => 'localhost',
        'username' => 'yaridagr_inspiremental',
        'password' => 'n*dMFX=i0-iq',
        'dbname' => 'yaridagr_inspiremental'
    ],
    [
        'servername' => 'localhost',
        'username' => 'yaridagr_inspire',
        'password' => 'n*dMFX=i0-iq',
        'dbname' => 'yaridagr_inspire'
    ]
];

// Production URL configuration
$productionUrlConfig = [
    'base_url' => 'https://inspiremental.org/',
    'admin_url' => 'https://inspiremental.org/admin/',
    'api_url' => 'https://inspiremental.org/api/',
    'assets_url' => 'https://inspiremental.org/assets/',
    'uploads_url' => 'https://inspiremental.org/uploads/'
];

// Force production settings
if (file_exists('force-production.flag')) {
    // Override environment detection
    define('FORCE_PRODUCTION', true);
    
    // Set production database credentials
    $servername = $productionDbConfig['servername'];
    $username = $productionDbConfig['username'];
    $password = $productionDbConfig['password'];
    $dbname = $productionDbConfig['dbname'];
    
    // Create connection
    $con = new mysqli($servername, $username, $password, $dbname);
    
    // Check connection
    if ($con->connect_error) {
        error_log("Production database connection failed: " . $con->connect_error);
        die("Database connection failed. Please contact the administrator.");
    }
    
    // Set character set
    $con->set_charset("utf8mb4");
    
    // Define production constants
    define('CURRENT_ENVIRONMENT', 'production');
    define('BASE_URL', $productionUrlConfig['base_url']);
    define('ADMIN_URL', $productionUrlConfig['admin_url']);
    define('API_URL', $productionUrlConfig['api_url']);
    define('ASSETS_URL', $productionUrlConfig['assets_url']);
    define('UPLOADS_URL', $productionUrlConfig['uploads_url']);
    define('DEBUG_MODE', false);
    
    // Disable error reporting for production
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
}

/**
 * Instructions for using this file:
 * 
 * 1. Upload all files to production server
 * 2. Create an empty file named 'force-production.flag' in the root directory
 * 3. Include this file at the top of db.php: include_once 'production-config.php';
 * 4. The website will use production settings regardless of auto-detection
 * 
 * To create the flag file via SSH/terminal:
 * touch force-production.flag
 * 
 * To create the flag file via FTP:
 * Create an empty text file and upload it as 'force-production.flag'
 */
?>
