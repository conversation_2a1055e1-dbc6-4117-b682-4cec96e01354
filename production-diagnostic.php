<?php
/**
 * Production Server Diagnostic
 * Identifies issues causing 500 errors
 */

// Enable error reporting to see what's wrong
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h1>🔍 Production Server Diagnostic</h1>";

echo "<h2>1. PHP Configuration:</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Error Reporting:</strong> " . (ini_get('display_errors') ? 'Enabled' : 'Disabled') . "</p>";
echo "<p><strong>Memory Limit:</strong> " . ini_get('memory_limit') . "</p>";
echo "<p><strong>Max Execution Time:</strong> " . ini_get('max_execution_time') . " seconds</p>";

echo "<h2>2. Server Environment:</h2>";
echo "<p><strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
echo "<p><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";
echo "<p><strong>Script Path:</strong> " . __FILE__ . "</p>";
echo "<p><strong>Current Directory:</strong> " . getcwd() . "</p>";

echo "<h2>3. File Permissions Check:</h2>";
$files_to_check = [
    'db.php',
    'api/test-handler.php',
    'config.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $perms = substr(sprintf('%o', fileperms($file)), -4);
        echo "<p style='color: green;'>✅ $file - Permissions: $perms</p>";
    } else {
        echo "<p style='color: red;'>❌ $file - File not found</p>";
    }
}

echo "<h2>4. Database Connection Test:</h2>";

try {
    // Test if db.php can be included
    if (file_exists('db.php')) {
        echo "<p style='color: green;'>✅ db.php file exists</p>";
        
        // Capture any output from db.php
        ob_start();
        include 'db.php';
        $db_output = ob_get_clean();
        
        if (!empty($db_output)) {
            echo "<p style='color: orange;'>⚠️ db.php output: " . htmlspecialchars($db_output) . "</p>";
        }
        
        // Check if connection was established
        if (isset($con) && $con instanceof mysqli) {
            if ($con->connect_error) {
                echo "<p style='color: red;'>❌ Database connection failed: " . $con->connect_error . "</p>";
            } else {
                echo "<p style='color: green;'>✅ Database connection successful</p>";
                
                // Test if tests table exists
                $result = $con->query("SHOW TABLES LIKE 'tests'");
                if ($result && $result->num_rows > 0) {
                    echo "<p style='color: green;'>✅ 'tests' table exists</p>";
                    
                    // Count tests
                    $count_result = $con->query("SELECT COUNT(*) as count FROM tests");
                    if ($count_result) {
                        $count = $count_result->fetch_assoc()['count'];
                        echo "<p style='color: blue;'>📊 Tests in database: $count</p>";
                    }
                } else {
                    echo "<p style='color: red;'>❌ 'tests' table does not exist</p>";
                }
            }
        } else {
            echo "<p style='color: red;'>❌ Database connection object not created</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ db.php file not found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Exception in database test: " . $e->getMessage() . "</p>";
}

echo "<h2>5. Test Handler API Test:</h2>";

try {
    if (file_exists('api/test-handler.php')) {
        echo "<p style='color: green;'>✅ api/test-handler.php file exists</p>";
        
        // Check if we can read the file
        $file_size = filesize('api/test-handler.php');
        echo "<p style='color: blue;'>📄 File size: $file_size bytes</p>";
        
        // Try to test the API internally
        $_GET['test_id'] = '1'; // Simulate the request
        
        echo "<p style='color: blue;'>🧪 Testing API internally...</p>";
        
        ob_start();
        try {
            include 'api/test-handler.php';
            $api_output = ob_get_clean();
            
            if (!empty($api_output)) {
                echo "<p style='color: green;'>✅ API executed successfully</p>";
                echo "<details><summary>API Output (first 500 chars)</summary>";
                echo "<pre>" . htmlspecialchars(substr($api_output, 0, 500)) . "...</pre>";
                echo "</details>";
            } else {
                echo "<p style='color: orange;'>⚠️ API executed but no output</p>";
            }
        } catch (Exception $e) {
            ob_end_clean();
            echo "<p style='color: red;'>❌ API execution failed: " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ api/test-handler.php file not found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Exception in API test: " . $e->getMessage() . "</p>";
}

echo "<h2>6. Environment Detection Test:</h2>";

$host = $_SERVER['HTTP_HOST'] ?? 'unknown';
$server_addr = $_SERVER['SERVER_ADDR'] ?? 'unknown';
$document_root = $_SERVER['DOCUMENT_ROOT'] ?? '';

echo "<p><strong>HTTP Host:</strong> $host</p>";
echo "<p><strong>Server Address:</strong> $server_addr</p>";
echo "<p><strong>Document Root:</strong> $document_root</p>";

// Test environment detection
if (strpos($host, 'inspiremental.org') !== false) {
    echo "<p style='color: green;'>✅ Production environment detected</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Environment detection may be incorrect</p>";
}

echo "<h2>7. Quick Fixes:</h2>";

echo "<h3>If Database Connection Failed:</h3>";
echo "<ol>";
echo "<li>Check database credentials in db.php</li>";
echo "<li>Verify database exists on server</li>";
echo "<li>Check database user permissions</li>";
echo "<li>Contact hosting provider if needed</li>";
echo "</ol>";

echo "<h3>If Tests Table Missing:</h3>";
echo "<ol>";
echo "<li>Import the database SQL file</li>";
echo "<li>Run the setup-database.php script</li>";
echo "<li>Create tables manually in cPanel</li>";
echo "</ol>";

echo "<h3>If File Permissions Issues:</h3>";
echo "<ol>";
echo "<li>Set PHP files to 644 permissions</li>";
echo "<li>Set directories to 755 permissions</li>";
echo "<li>Check .htaccess file permissions</li>";
echo "</ol>";

echo "<h3>If API Still Fails:</h3>";
echo "<ol>";
echo "<li>Check server error logs</li>";
echo "<li>Verify PHP version compatibility</li>";
echo "<li>Test with simple PHP file first</li>";
echo "<li>Contact hosting support</li>";
echo "</ol>";

echo "<h2>8. Test Links:</h2>";
echo "<p><a href='api/test-handler?test_id=1' target='_blank'>Test API Directly</a></p>";
echo "<p><a href='online-test' target='_blank'>Test Online Test Page</a></p>";
echo "<p><a href='test-environment.php' target='_blank'>Environment Test</a></p>";

echo "<div style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; margin: 20px 0;'>";
echo "<h3>⚠️ Security Warning</h3>";
echo "<p><strong>Delete this file after diagnosis!</strong></p>";
echo "</div>";

echo "<h2>9. Server Error Log Check:</h2>";
echo "<p>Check your hosting control panel for error logs. Look for:</p>";
echo "<ul>";
echo "<li>PHP Fatal Errors</li>";
echo "<li>Database connection errors</li>";
echo "<li>Permission denied errors</li>";
echo "<li>File not found errors</li>";
echo "</ul>";
?>
