<?php
/**
 * Simple Database Test
 * Tests database connection and shows current status
 */

echo "<h1>🔍 Database Connection Test</h1>";

// Test database connection
try {
    $config = [
        'servername' => 'localhost',
        'username' => 'yaridagr_inspiremental',
        'password' => 'n*dMFX=i0-iq',
        'dbname' => 'yaridagr_inspiremental'
    ];
    
    $con = new mysqli($config['servername'], $config['username'], $config['password'], $config['dbname']);
    
    if ($con->connect_error) {
        echo "<p style='color: red;'>❌ Connection failed: " . $con->connect_error . "</p>";
        
        // Try alternative database name
        $config['dbname'] = 'inspiremental';
        $con2 = new mysqli($config['servername'], $config['username'], $config['password'], $config['dbname']);
        
        if ($con2->connect_error) {
            echo "<p style='color: red;'>❌ Alternative connection failed: " . $con2->connect_error . "</p>";
        } else {
            echo "<p style='color: green;'>✅ Connected to alternative database: " . $config['dbname'] . "</p>";
            $con = $con2;
        }
    } else {
        echo "<p style='color: green;'>✅ Connected to database: " . $config['dbname'] . "</p>";
    }
    
    if (!$con->connect_error) {
        // Test tests table
        $result = $con->query("SHOW TABLES LIKE 'tests'");
        if ($result && $result->num_rows > 0) {
            echo "<p style='color: green;'>✅ 'tests' table exists</p>";
            
            // Count tests
            $count_result = $con->query("SELECT COUNT(*) as count FROM tests");
            if ($count_result) {
                $count = $count_result->fetch_assoc()['count'];
                echo "<p style='color: blue;'>📊 Tests in database: $count</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ 'tests' table does not exist</p>";
        }
        
        $con->close();
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>";
}

echo "<h2>Test API:</h2>";
echo "<p><a href='api/test-handler?test_id=1' target='_blank'>🧪 Test API: test_id=1</a></p>";
echo "<p><a href='api/test-handler?test_id=6' target='_blank'>🧪 Test API: test_id=6</a></p>";

echo "<h2>Test Pages:</h2>";
echo "<p><a href='home' target='_blank'>🏠 Home</a></p>";
echo "<p><a href='awarenesses' target='_blank'>🧠 Awarenesses</a></p>";
echo "<p><a href='online-test' target='_blank'>📋 Online Test</a></p>";

echo "<div style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; margin: 20px 0;'>";
echo "<h3>⚠️ Note</h3>";
echo "<p>Delete this file after testing!</p>";
echo "</div>";
?>
