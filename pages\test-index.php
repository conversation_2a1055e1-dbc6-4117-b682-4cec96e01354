<div class="container mt-5">
    <div class="row">
        <div class="col-12">
            <h1>🧪 تێستەکانی دەروونی</h1>
            <p class="lead">تێستەکانی جۆراوجۆری دەروونی بۆ هەڵسەنگاندنی دۆخی دەروونیت</p>
        </div>
    </div>
    
    <div class="row mt-4">
        <?php
        // Get available tests from database
        try {
            $testsQuery = $con->query("SELECT id, title, description FROM tests ORDER BY id");
            if ($testsQuery && $testsQuery->num_rows > 0) {
                while ($test = $testsQuery->fetch_assoc()) {
                    echo '<div class="col-md-6 col-lg-4 mb-4">';
                    echo '<div class="card h-100">';
                    echo '<div class="card-body">';
                    echo '<h5 class="card-title">' . htmlspecialchars($test['title']) . '</h5>';
                    if ($test['description']) {
                        echo '<p class="card-text">' . htmlspecialchars($test['description']) . '</p>';
                    }
                    echo '<a href="api/test-handler?test_id=' . $test['id'] . '" class="btn btn-primary">دەستپێکردنی تێست</a>';
                    echo '</div>';
                    echo '</div>';
                    echo '</div>';
                }
            } else {
                echo '<div class="col-12">';
                echo '<div class="alert alert-warning">';
                echo '<h4>هیچ تێستێک نەدۆزرایەوە</h4>';
                echo '<p>تکایە دواتر هەوڵ بدەوە یان بە بەڕێوەبەر پەیوەندی بکە.</p>';
                echo '</div>';
                echo '</div>';
            }
        } catch (Exception $e) {
            echo '<div class="col-12">';
            echo '<div class="alert alert-danger">';
            echo '<h4>هەڵەیەک ڕوویدا</h4>';
            echo '<p>نەتوانرا تێستەکان بخوێنرێنەوە. تکایە دواتر هەوڵ بدەوە.</p>';
            echo '</div>';
            echo '</div>';
        }
        ?>
    </div>
    
    <div class="row mt-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>زانیاری گرنگ</h5>
                </div>
                <div class="card-body">
                    <ul>
                        <li>تێستەکان بۆ مەبەستی زانیاری و ڕێنمایین</li>
                        <li>ئەنجامەکان جێگرەوەی ڕاوێژکاری پسپۆڕانە نین</li>
                        <li>ئەگەر هەست بە کێشەی گەورەی دەروونی دەکەیت، تکایە بە پسپۆڕ پەیوەندی بکە</li>
                        <li>هەموو زانیارییەکانت بە تەواوی نهێنی دەمێننەوە</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12 text-center">
            <a href="home" class="btn btn-secondary">گەڕانەوە بۆ سەرەکی</a>
            <a href="contact" class="btn btn-info">پەیوەندی بە پسپۆڕ</a>
        </div>
    </div>
</div>
