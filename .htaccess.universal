RewriteEngine On

# Universal .htaccess configuration
# Works automatically for both localhost and production environments
# No manual configuration needed!

# Redirect base URL to home page
RewriteRule ^$ home [R=301,L]

# Redirect old index.php URLs to clean URLs
RewriteCond %{THE_REQUEST} \s/+[^/\s]*\.php\?page=([^\s&]+) [NC]
RewriteRule ^ %1? [R=301,L]

# Admin panel routing
RewriteRule ^admin/?$ admin/index.php [L]
RewriteRule ^admin/([^/]+)/?$ admin/$1.php [L]

# API routing  
RewriteRule ^api/([^/]+)/?$ api/$1.php [L]

# Main page routing - Universal approach
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^.*/admin/ [NC]
RewriteCond %{REQUEST_URI} !^.*/api/ [NC]
RewriteCond %{REQUEST_URI} !^.*/assets/ [NC]
RewriteCond %{REQUEST_URI} !^.*/uploads/ [NC]
RewriteCond %{REQUEST_URI} !^.*/img/ [NC]
RewriteCond %{REQUEST_URI} !^.*/font/ [NC]
RewriteCond %{REQUEST_URI} !^.*/guide/ [NC]
RewriteCond %{REQUEST_URI} !^.*\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|pdf|zip|rar)$ [NC]
RewriteRule ^([^/]+)/?$ index.php?page=$1 [L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# Prevent access to sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

<Files ~ "\.sql$">
    Order allow,deny
    Deny from all
</Files>

<Files ~ "\.md$">
    Order allow,deny
    Deny from all
</Files>

<Files ~ "deploy\.php$">
    Order allow,deny
    Deny from all
</Files>

<Files ~ "test-environment\.php$">
    Order allow,deny
    Deny from all
</Files>

# Enable compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Browser caching for better performance
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType application/pdf "access plus 1 month"
</IfModule>

# Force HTTPS on production (uncomment for production)
# RewriteCond %{HTTPS} off
# RewriteCond %{HTTP_HOST} !^(localhost|127\.0\.0\.1) [NC]
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Prevent hotlinking (uncomment for production)
# RewriteCond %{HTTP_REFERER} !^$
# RewriteCond %{HTTP_REFERER} !^https?://(www\.)?inspiremental\.org [NC]
# RewriteCond %{HTTP_REFERER} !^https?://(www\.)?localhost [NC]
# RewriteRule \.(jpg|jpeg|png|gif|svg)$ - [F]
