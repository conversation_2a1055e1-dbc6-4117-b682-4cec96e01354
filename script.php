<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/2.10.2/umd/popper.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="assets/js/script.js"></script>
<script>
document.addEventListener("DOMContentLoaded", function () {
    const container = document.querySelector(".container-fluid");
    const styleElement = document.createElement("style");

    function applyPaddingLeft(paddingValue) {
        container.style.paddingLeft = paddingValue;
    }

    function checkScrollbar() {
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            // Always apply padding on mobile screens
            applyPaddingLeft("calc(var(--bs-gutter-x) * .70)");
        } else {
            // For larger screens, adjust padding based on scrollbar presence
            if (document.documentElement.scrollHeight > window.innerHeight) {
                applyPaddingLeft("0"); // Remove padding if there’s a scrollbar
            } else {
                applyPaddingLeft("calc(var(--bs-gutter-x) * .70)"); // Default padding
            }
        }
    }

    // Initial check and event listeners for scroll and resize
    checkScrollbar();
    window.addEventListener("resize", checkScrollbar);
    window.addEventListener("scroll", checkScrollbar);

    // Modal open event: Adjust padding and scrollbar gutter based on scrollbar presence
    document.addEventListener("show.bs.modal", function () {
        if (document.documentElement.scrollHeight <= window.innerHeight) {
            // No scrollbar: apply left padding when modal opens
            applyPaddingLeft("calc(var(--bs-gutter-x) * .70)");
        } else {
            // Scrollbar exists, apply default padding and add gutter style
            applyPaddingLeft("calc(var(--bs-gutter-x) * .70)");
            styleElement.innerHTML = `html { scrollbar-gutter: auto !important; }`;
            document.head.appendChild(styleElement);
        }
    });

    // Modal close event: Reset padding and remove scrollbar-gutter style if applied
    document.addEventListener("hidden.bs.modal", function () {
        checkScrollbar(); // Re-check padding based on the scrollbar status
        if (document.head.contains(styleElement)) {
            document.head.removeChild(styleElement); // Remove scrollbar-gutter style
        }
    });
});


</script>
