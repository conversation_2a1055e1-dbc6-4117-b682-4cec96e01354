<?php
header('Content-Type: text/html; charset=utf-8');
// Database connection - production only
$db_path = dirname(__FILE__) . '/../db.php';
if (file_exists($db_path)) {
    include_once $db_path;
} else {
    die("Database configuration file not found.");
}

// Convert mysqli connection to PDO for compatibility
// Use the existing database connection
if (isset($con) && !$con->connect_error) {
    // Production database configuration
    $config = [
        'servername' => 'localhost',
        'username' => 'yaridagr_inspiremental',
        'password' => 'n*dMFX=i0-iq',
        'dbname' => 'yaridagr_inspiremental'
    ];

    try {
        $pdo = new PDO("mysql:host={$config['servername']};dbname={$config['dbname']};charset=utf8mb4",
                       $config['username'], $config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    } catch(PDOException $e) {
        die("Connection failed: " . $e->getMessage());
    }
} else {
    die("Database connection not available.");
}

// Get test ID from URL parameter
$test_id = isset($_GET['test_id']) ? (int)$_GET['test_id'] : 1;

// Fetch test data from database
try {
    $stmt = $pdo->prepare("SELECT * FROM tests WHERE id = ?");
    $stmt->execute([$test_id]);
    $test = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$test) {
        die("Test not found");
    }
    
    $questions = json_decode($test['questions'], true);
    if (!$questions) {
        die("Invalid test data");
    }
    
} catch(PDOException $e) {
    die("Error fetching test: " . $e->getMessage());
}

// Handle test submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $answers = json_decode(file_get_contents('php://input'), true);
    
    if ($answers && isset($answers['scores'])) {
        $total_score = array_sum($answers['scores']);
        
        // Save test result to database (optional - only if user is logged in)
        session_start();
        if (isset($_SESSION['user_id'])) {
            try {
                $stmt = $pdo->prepare("INSERT INTO test_results (user_id, test_id, answers, total_score) VALUES (?, ?, ?, ?)");
                $stmt->execute([$_SESSION['user_id'], $test_id, json_encode($answers['scores']), $total_score]);
            } catch(PDOException $e) {
                // Log error but don't fail the response
                error_log("Error saving test result: " . $e->getMessage());
            }
        }
        
        // Return result
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'total_score' => $total_score,
            'max_score' => count($questions) * 4,
            'scoring_info' => $test['scoring_info']
        ]);
        exit;
    }
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ckb">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($test['test_name_kurdish']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link href="style.css" rel="stylesheet">

</head>
<body class="test-handler-body">
    <!-- Simple Header with Back Button and Logo -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a href="online-test" class="btn text-light me-3" style="font-size: 1.5rem; height: 70px; display: flex; align-items: center;" title="گەڕانەوە">
                <i class="bi bi-arrow-right"></i>
            </a>
            <a class="navbar-brand" href="home">
                <img src="img/logo.png" alt="" height="50px" class="" style="margin-right: -20px !important;">
                <p class="d-inline h5 align-middle pe-1" style="font-family: RabarBold !important;">ئیلهامبەخشی دەروونی</p>
            </a>
        </div>
    </nav>

    <div class="test-wrapper">
    <div class="test-container">
        <h3 class="testTitle"><?php echo htmlspecialchars($test['test_name_kurdish']); ?></h3>
        <div class="test-progress-bar">
            <div class="progress" id="progress"></div>
        </div>
        <div class="question-container" id="question-container">
            <!-- Questions will be dynamically inserted here -->
        </div>
        <div class="test-navigation">
            <button id="prev" disabled>پێشوو</button>
            <p id="slide">پرسیار 1 لە <?php echo count($questions); ?></p>
            <button id="next" disabled>دواتر</button>
        </div>
    </div>

    <script>
    const questions = <?php echo json_encode($questions, JSON_UNESCAPED_UNICODE); ?>;
    const testId = <?php echo $test_id; ?>;
    const scoringInfo = <?php echo json_encode($test['scoring_info'], JSON_UNESCAPED_UNICODE); ?>;
    
    const scores = Array(questions.length).fill(null);
    let currentQuestion = 0;
    let highestReachedQuestion = 0;

    const progressElement = document.getElementById("progress");
    const questionContainer = document.getElementById("question-container");
    const prevButton = document.getElementById("prev");
    const nextButton = document.getElementById("next");
    const slideCounter = document.getElementById("slide");

    function renderQuestion(slideDirection = 'left') {
        // Create question element
        const questionElement = document.createElement("div");
        questionElement.classList.add("test-question");

        // Set initial position based on slide direction for RTL
        // 'left' means sliding in from left (next question in RTL)
        // 'right' means sliding in from right (previous question in RTL)
        const initialOffset = slideDirection === 'left' ? '-100%' : '100%';
        questionElement.style.transform = `translateX(${initialOffset})`;

        // Create question text
        const questionTextElement = document.createElement("div");
        questionTextElement.classList.add("test-question-text");
        questionTextElement.textContent = questions[currentQuestion];
        questionElement.appendChild(questionTextElement);

        // Create options
        const optionsElement = document.createElement("div");
        optionsElement.classList.add("test-options");
        ["هەرگیز", "جارێک", "چەند جار", "زۆر جار", "هەمیشە"].forEach((option, index) => {
            const button = document.createElement("button");
            button.textContent = option;
            button.onclick = () => selectAnswer(index);

            // Highlight previously selected option
            if (scores[currentQuestion] === index) {
                button.style.background = "#ed7014";
                button.style.color = "white";
                button.style.transform = "translateY(-2px)";
                button.style.boxShadow = "0 5px 15px rgba(237, 112, 20, 0.3)";
            }

            optionsElement.appendChild(button);
        });
        questionElement.appendChild(optionsElement);

        questionContainer.appendChild(questionElement);

        // Trigger sliding animation
        setTimeout(() => {
            questionElement.style.transform = "translateX(0)";
        }, 10);

        // Update progress bar
        progressElement.style.width = `${((currentQuestion + 1) / questions.length) * 100}%`;

        // Update navigation buttons and slide counter
        prevButton.disabled = currentQuestion === 0;
        nextButton.disabled = scores[currentQuestion] === null;
        slideCounter.textContent = `پرسیار ${currentQuestion + 1} لە ${questions.length}`;
    }

    function slideOut(direction, callback) {
        const questionElement = questionContainer.querySelector(".test-question");

        if (!questionElement) {
            callback();
            return;
        }

        // For RTL: 'right' means sliding out to the right (going to next question)
        // 'left' means sliding out to the left (going to previous question)
        const outgoingOffset = direction === "right" ? "100%" : "-100%";

        // Slide out current question
        questionElement.style.transform = `translateX(${outgoingOffset})`;

        setTimeout(() => {
            // Clear the container before callback
            questionContainer.innerHTML = "";
            callback();
        }, 500);
    }

    function selectAnswer(index) {
        scores[currentQuestion] = index;

        // Update the highest reached question
        if (currentQuestion >= highestReachedQuestion) {
            highestReachedQuestion = currentQuestion + 1;
        }

        nextButton.disabled = false;

        if (currentQuestion < questions.length - 1) {
            // RTL: Slide current question out to the right, new question slides in from left
            slideOut("right", () => {
                currentQuestion++;
                renderQuestion('left'); // New question slides in from left
            });
        } else {
            showResults();
        }
    }

    prevButton.onclick = () => {
        if (currentQuestion > 0) {
            // RTL: Slide current question out to the left, previous question slides in from right
            slideOut("left", () => {
                currentQuestion--;
                renderQuestion('right'); // Previous question slides in from right
            });
        }
    };

    nextButton.onclick = () => {
        if (currentQuestion < questions.length - 1) {
            // RTL: Slide current question out to the right, next question slides in from left
            slideOut("right", () => {
                currentQuestion++;
                if (currentQuestion > highestReachedQuestion) {
                    highestReachedQuestion = currentQuestion;
                }
                renderQuestion('left'); // Next question slides in from left
            });
        } else {
            showResults();
        }
    };

    function showResults() {
        // Send results to server
        fetch(window.location.href, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                scores: scores,
                test_id: testId
            })
        })
        .then(response => response.json())
        .then(data => {
            // Use RTL sliding for result screen (same as next question)
            slideOut("right", () => {
                // Format the scoring info with proper line breaks and English numbers
                const formattedInfo = data.scoring_info
                    .replace(/(\d+)/g, (match) => match) // Keep English numbers
                    .replace(/\./g, '.<br>') // Add line breaks after periods
                    .replace(/<br><br>/g, '<br>'); // Remove double line breaks

                questionContainer.innerHTML = `
                    <div class="test-result-container">
                        <h2>تەواو بوو!</h2>
                        <div class="test-score-display">${data.total_score} نمرە</div>
                        <div class="test-scoring-info">
                            <strong>لێکدانەوەی ئەنجام:</strong><br><br>
                            ${formattedInfo}
                        </div>
                        <button onclick="window.history.back()" style="background: #ed7014; color: white; border: none; padding: 15px 30px; border-radius: 25px; cursor: pointer; font-size: 1em; margin-top: 20px;">گەڕانەوە</button>
                    </div>
                `;

                // Hide navigation buttons
                document.querySelector('.test-navigation').style.display = 'none';
            });
        })
        .catch(error => {
            console.error('Error:', error);
            alert('هەڵەیەک ڕوویدا لە پاشەکەوتکردنی ئەنجامەکان');
        });
    }

    // Initial render
    renderQuestion();
    </script>
    </div> <!-- Close test-wrapper -->

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
