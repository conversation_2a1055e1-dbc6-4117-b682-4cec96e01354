<?php
/**
 * Simple API Test
 * Basic test to check if API directory and PHP work
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/html; charset=utf-8');

echo "<h1>🧪 Simple API Test</h1>";

echo "<h2>Basic PHP Test:</h2>";
echo "<p style='color: green;'>✅ PHP is working</p>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>";

echo "<h2>Server Info:</h2>";
echo "<p><strong>HTTP Host:</strong> " . ($_SERVER['HTTP_HOST'] ?? 'Unknown') . "</p>";
echo "<p><strong>Request URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "</p>";
echo "<p><strong>Script Name:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Unknown') . "</p>";

echo "<h2>File System Test:</h2>";
echo "<p><strong>Current Directory:</strong> " . getcwd() . "</p>";
echo "<p><strong>Parent Directory Exists:</strong> " . (is_dir('../') ? 'Yes' : 'No') . "</p>";
echo "<p><strong>db.php Exists:</strong> " . (file_exists('../db.php') ? 'Yes' : 'No') . "</p>";

echo "<h2>Database Connection Test:</h2>";
try {
    if (file_exists('../db.php')) {
        include '../db.php';
        
        if (isset($con) && !$con->connect_error) {
            echo "<p style='color: green;'>✅ Database connection successful</p>";
            
            // Test simple query
            $result = $con->query("SELECT 1 as test");
            if ($result) {
                echo "<p style='color: green;'>✅ Database query successful</p>";
            } else {
                echo "<p style='color: red;'>❌ Database query failed: " . $con->error . "</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Database connection failed</p>";
            if (isset($con)) {
                echo "<p><strong>Error:</strong> " . $con->connect_error . "</p>";
            }
        }
    } else {
        echo "<p style='color: red;'>❌ db.php file not found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>";
}

echo "<h2>GET Parameters:</h2>";
if (!empty($_GET)) {
    foreach ($_GET as $key => $value) {
        echo "<p><strong>$key:</strong> " . htmlspecialchars($value) . "</p>";
    }
} else {
    echo "<p>No GET parameters</p>";
}

echo "<h2>Test Results:</h2>";
echo "<p>If you can see this page, the API directory and basic PHP functionality are working.</p>";

echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li>If database connection failed, check credentials</li>";
echo "<li>If this works but test-handler doesn't, check test-handler.php for errors</li>";
echo "<li>Check server error logs for specific error messages</li>";
echo "</ol>";

echo "<p><a href='../production-diagnostic.php'>Run Full Diagnostic</a></p>";
echo "<p><a href='test-handler?test_id=1'>Test Original Handler</a></p>";
?>
