@font-face {
  font-family: Rabar;
  src: url(font/Rabar_22.ttf);
}
@font-face {
  font-family: RabarBold;
  src: url(font/Rabar_21.ttf);
}
* {
  font-family: Rabar !important;
}
html {
  scrollbar-gutter: stable !important;
}
body {
  background-color: #d5f6ff !important;
}
.text-teal {
  color: #265e6d !important;
}
.bg-teal {
  background-color: #265e6d !important;
}
.border-teal {
  border: 3px solid #265e6d !important;
}
.button-teal {
  background-color: #265e6d !important;
  border: none !important;
  outline: none !important;
}
.button-teal:hover {
  background-color: #10af9c !important;
  border: none !important;
  outline: none !important;
}
.no-outline {
  border: none !important;
  outline: none !important;
}
.modal-content {
  border-radius: 0 !important;
  border: none !important;
  outline: none !important;
  background-color: #d5f6ff !important;
}
nav {
  background-color: #265e6d !important;
  font-family: RabarBold !important;
  font-size: large !important;
  width: 100%;
  position: fixed !important;
  top: 0 !important;
  z-index: 1000;
}
nav a p {
  color: whitesmoke !important;
}
ul {
  padding-inline-start: 0 !important;
}
ul li a {
  color: whitesmoke !important;
}
.centered {
  display: flex;
  justify-content: center;
  align-items: center;
}
.container {
  padding-top: 100px !important;
}
.team-member p {
  text-indent: 25px;
}
.inLess {
  text-indent: 0 !important;
  font-family: RabarBold !important;
}
.mainl,
.awarei {
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
}
.mainlist {
  width: 100% !important;
  position: fixed !important;
  bottom: 0 !important;
}
.textTalk {
  transform: rotate(180deg);
}
.talkBox {
  display: none;
  width: auto;
  height: auto;
  text-align: center;
  background: #ddd;
  padding: 5px;
  margin: 35px auto;
  position: relative;
  border-radius: 10px;
  position: fixed;
  transform: rotate(180deg);
  bottom: 4em;
}
.talkBox:before {
  content: "";
  display: block;
  width: 0;
  border-width: 15px 15px 0px 15px;
  border-color: #ddd transparent;
  border-style: solid;
  position: absolute;
  bottom: -15px;
  right: 50%;
  transform: translateX(50%);
}
.active {
  background-color: rgba(16, 175, 156, 0.75) !important;
  height: 70px !important;
  padding: 0 !important;
  margin: 0 !important;
}
.nav-item:hover,
.active:hover {
  background-color: rgba(16, 175, 156, 0.5) !important;
  height: 70px !important;
  margin: 0 !important;
}
.navbar {
  padding: 0;
  margin: 0;
  border: 0;
  display: flex;
  align-items: center;
}
.nav-link {
  height: 70px !important;
  padding: 0 10px !important;
  margin: 0 !important;
  font-size: medium;
}
.nav-item {
  display: flex;
  align-items: center;
}
@media only screen and (max-width: 720px) {
  .nav-item,
  .active,
  .nav-link,
  .nav-item:hover,
  .active:hover {
    height: 2em !important;
    width: 100%;
  }
}
h1 {
  font-size: 5vw;
}
h2 {
  font-size: 3.5vw;
}
@media screen and (max-width: 1300px) {
  h1 {
    font-size: 9vw;
  }
  h2 {
    font-size: 5vw;
  }
}
@media screen and (max-width: 600px) {
  h1 {
    font-size: 15vw;
  }
  h2 {
    font-size: 10.5vw;
  }
}
.nave-item {
  height: auto !important;
  width: 100% !important;
  border-radius: none !important;
}
.nave-item:hover {
  background-color: rgba(16, 175, 156, 0.5) !important;
  height: auto !important;
  width: 100% !important;
  border-radius: none !important;
}
.hover-red:hover {
  background-color: rgba(175, 16, 16, 0.5) !important;
}
#popup {
  display: none;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 30px;
  background-color: #f9f9f9;
  border: 2px solid #ccc;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  text-align: center;
  width: 100%;
  height: 100vh;
}

#emoji-container {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
}

@media screen and (max-width: 600px) {
  #popup {
    padding-top: 18em;
  }
}

.emoji {
  cursor: pointer;
  font-size: 15vw;
  margin: 0 10px;
}

#popup p {
  font-size: 10vw;
  margin-bottom: 20px;
}

/*----------AWARENESS----------*/
.awareBox {
  height: 100% !important;
}
.awareMiniBox {
  height: 100% !important;
}
.aware {
  display: flex;
  justify-content: center;
  align-items: end;
}
.awarei {
  width: 66%;
  position: relative;
}

/* Apply hue filter to bot icons */
.awarei[src*="bot/"] {
  filter: hue-rotate(-33deg) saturate(175%) brightness(95%);
}
.awareLabel {
  position: absolute;
  text-decoration: none;
  width: auto;
  font-size: larger;
  background-color: rgba(46, 133, 98, 0.75);
  color: #f8f9fa;
  padding: 0 1em;
  margin-bottom: 1em;
  border-radius: 3em;
  transition: scale 0.25s;
}
.awareLabel:hover {
  scale: 1.2;
}
.awareLabel2 {
  position: absolute;
  text-decoration: none;
  width: auto;
  font-size: x-large;
  background-color: rgba(46, 133, 98, 0.9);
  color: #f8f9fa;
  padding: 0 1em;
  margin-bottom: 1em;
  border-radius: 3em;
  transition: scale 0.25s;
}
.awareLabel2:hover {
  scale: 1.2;
}

.imgP img {
  width: 75%;
  display: flex;
  justify-content: center;
  align-items: start;
}
@media screen and (max-width: 600px) {
  .imgP img {
    width: 100%;
  }
}
.textBG {
  background-color: #ffffff84;
}
.textBG p {
  text-indent: 25px;
}
.backBtn {
  cursor: pointer;
  position: fixed;
  bottom: 0;
  right: 0;
  color: #ed7014;
}
.textBox {
  background-color: #f8f9fa;
  width: auto;
  height: 100px;
}

/*----------TEST----------*/
.testLabel {
  text-decoration: none;
  color: #ed7014;
  font-size: x-large;
}

/*----------FIND CENTER----------*/
.gMapsText {
  background-color: rgba(0, 0, 0, 0.75);
  color: #f8f9fa;
}

/*----------MODERN CHAT INTERFACE----------*/
.modern-chat-wrapper {
  max-width: 800px;
  margin: 120px auto 20px auto;
  padding: 0 15px;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
}

.language-toggle {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  gap: 10px;
}

.lang-btn {
  padding: 8px 20px;
  border: 2px solid #ed7014;
  background: transparent;
  color: #ed7014;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.lang-btn.active,
.lang-btn:hover {
  background: #ed7014;
  color: white;
}

.modern-chat-container {
  background: #ffffff;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #e1e5e9;
  height: 75vh;
  max-height: 700px;
  display: flex;
  flex-direction: column;
}

.chat-header {
  background: linear-gradient(135deg, #ed7014 0%, #f39c12 100%);
  color: white;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  flex-shrink: 0;
}

.bot-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition: all 0.3s ease;
}

.bot-avatar img {
  width: 35px;
  height: 35px;
  object-fit: contain;
  filter: hue-rotate(-33deg) saturate(175%) brightness(95%);
  transition: all 0.3s ease;
}

.bot-avatar img:hover {
  transform: scale(1.1);
}

.avatar-fallback {
  font-size: 24px;
  display: none;
}

.bot-info {
  flex: 1;
}

.bot-info h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.online-status {
  font-size: 12px;
  opacity: 0.9;
  display: flex;
  align-items: center;
  gap: 5px;
}

.online-status::before {
  content: "●";
  color: #4caf50;
  font-size: 10px;
}

.chat-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.modern-chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #f8f9fa;
  min-height: 0;
}

.welcome-message {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.bot-avatar-small {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: rgba(237, 112, 20, 0.1);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
  overflow: hidden;
}

.bot-avatar-small img {
  width: 25px;
  height: 25px;
  object-fit: contain;
  filter: hue-rotate(-33deg) saturate(175%) brightness(95%);
}

.message-content {
  flex: 1;
}

.message-content p {
  background: white;
  padding: 15px;
  border-radius: 15px;
  margin: 0 0 15px 0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
}

.suggested-questions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggested-question {
  background: #ed7014;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.3s ease;
}

.suggested-question:hover {
  background: #d35400;
  transform: translateY(-1px);
}
.chat-message {
  display: flex;
  gap: 12px;
  margin-bottom: 15px;
  animation: fadeInUp 0.3s ease;
}

.chat-message.user-message {
  flex-direction: row-reverse;
}

.chat-message.user-message .message-bubble {
  background: #ed7014;
  color: white;
}

.chat-message.bot-message .message-bubble {
  background: white;
  color: #333;
  border: 1px solid #e1e5e9;
}

.message-bubble {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 18px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  position: relative;
}

.message-content {
  margin-bottom: 5px;
  line-height: 1.4;
}

.message-time {
  font-size: 11px;
  opacity: 0.7;
  text-align: right;
}

.typing .message-bubble {
  background: #f0f0f0;
  color: #666;
  font-style: italic;
}

.typing .message-content::after {
  content: "...";
  animation: typingDots 1.5s infinite;
}

@keyframes typingDots {
  0%,
  20% {
    content: "...";
  }
  40% {
    content: "....";
  }
  60% {
    content: ".....";
  }
  80%,
  100% {
    content: "...";
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modern-chat-input {
  background: white;
  padding: 20px;
  border-top: 1px solid #e1e5e9;
  flex-shrink: 0;
}

.input-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.input-container input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 25px;
  outline: none;
  font-size: 14px;
  transition: all 0.3s ease;
}

.input-container input:focus {
  border-color: #ed7014;
  box-shadow: 0 0 0 3px rgba(237, 112, 20, 0.1);
}

.send-button {
  width: 45px;
  height: 45px;
  border: none;
  background: #ed7014;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-button:hover {
  background: #d35400;
  transform: scale(1.05);
}

.input-footer {
  text-align: center;
  margin-top: 10px;
}

.input-footer small {
  color: #666;
  font-size: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-chat-wrapper {
    margin: 100px 10px 10px 10px;
    padding: 0;
  }

  .modern-chat-container {
    height: 85vh;
    max-height: 600px;
  }

  .message-bubble {
    max-width: 85%;
  }

  .chat-header {
    padding: 15px;
  }

  .bot-info h3 {
    font-size: 16px;
  }

  .modern-chat-input {
    padding: 15px;
  }
}

/* RTL Support for Kurdish */
[dir="rtl"] .chat-message.user-message {
  flex-direction: row;
}

[dir="rtl"] .chat-message.bot-message {
  flex-direction: row-reverse;
}

[dir="rtl"] .message-time {
  text-align: left;
}

[dir="rtl"] .input-container {
  direction: rtl;
}

[dir="rtl"] .suggested-questions {
  direction: rtl;
}

/*----------ONLINE CONTACT----------*/
.contact-card {
  position: relative;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 400px;
  width: 100%;
  text-align: center;
}

.status-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 1em;
  height: 1em;
  background-color: #4caf50;
  border-radius: 50%;
  border: 2px solid #fff;
}
.offline {
  background-color: #f44336; /* Red color for offline status */
}
.profile-section {
  padding: 20px;
}

.profile-section img {
  border-radius: 50%;
  width: 120px;
  height: 120px;
  object-fit: cover;
  margin-bottom: 10px;
}

#name {
  margin: 0;
  color: #333;
}

#email,
#phone {
  color: #666;
  margin: 5px 0;
}

.social-icons {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px;
  background-color: #f5f5f5;
}

.social-icons a {
  margin: 0 10px;
  text-decoration: none;
  color: #333;
  transition: color 0.3s;
}

.social-icons a:hover {
  color: #007bff;
}

.social-icons img {
  width: 30px;
  height: 30px;
}

/*----------ABOUT PAGE MODERN STYLING----------*/

/* Hero Section */
.about-hero {
  background: linear-gradient(135deg, #265e6d 0%, #10af9c 100%);
  color: white;
  padding: 120px 0 80px;
  position: relative;
  overflow: hidden;
}

.about-hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("img/brain.png") repeat;
  background-size: 40px 40px;
  opacity: 0.15;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 1.4rem;
  margin-bottom: 3rem;
  opacity: 0.9;
  line-height: 1.6;
}

.hero-stats {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 1.5rem;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  color: #ed7014;
}

.stat-label {
  font-size: 1rem;
  opacity: 0.9;
}

.hero-image {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.floating-card {
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  padding: 3rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.floating-card .logo-container {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.floating-card .logo-container img {
  height: 50px;
  width: auto;
}

.floating-card .logo-text {
  color: #265e6d;
  font-weight: 600;
  font-size: 1.2rem;
}

.floating-card h4 {
  font-weight: 600;
  color: #333;
}

/* Mission Section */
.mission-section {
  padding: 100px 0;
  background: #d5f6ff;
}

.section-title {
  font-size: 2.8rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1rem;
  position: relative;
  text-align: center;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #ff7300, #10af9c);
  border-radius: 2px;
}

.section-subtitle {
  font-size: 1.2rem;
  color: #6c757d;
  line-height: 1.7;
  max-width: 600px;
  margin: 0 auto 2rem;
}

.mission-card {
  background: white;
  padding: 2.5rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: 100%;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.mission-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.mission-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #ff7300, #10af9c);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  transition: transform 0.3s ease;
}

.mission-card:hover .mission-icon {
  transform: scale(1.1);
}

.mission-icon i {
  font-size: 2rem;
  color: white;
}

.mission-card h4 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.mission-card p {
  color: #6c757d;
  line-height: 1.6;
  font-size: 1rem;
}

/* Team Section */
.team-section {
  padding: 100px 0;
  background: white;
}

.team-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  height: 100%;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.team-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.team-image {
  position: relative;
  overflow: hidden;
  height: 300px;
}

.team-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.team-card:hover .team-image img {
  transform: scale(1.05);
  filter: brightness(1.1);
}

.team-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(38, 94, 109, 0.8),
    rgba(237, 112, 20, 0.8)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.team-card:hover .team-overlay {
  opacity: 1;
}

.team-social {
  display: flex;
  gap: 1rem;
}

.social-link {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.social-link:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
  color: white;
}

.team-content {
  padding: 2rem;
}

.team-content h4 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.team-role {
  color: #ed7014;
  font-weight: 500;
  margin-bottom: 1rem;
  font-size: 1rem;
}

.team-bio {
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
}

.team-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.skill-tag {
  background: linear-gradient(30deg, #ff7300, #10af9c);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Values Section */
.values-section {
  padding: 100px 0;
  background: url("img/BGtree.jpg") center/cover no-repeat;
  position: relative;
}

.values-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(213, 246, 255, 0.9);
  backdrop-filter: blur(3px);
  z-index: 1;
}

.values-section .container {
  position: relative;
  z-index: 2;
}

.values-content {
  padding: 2rem 0;
}

.value-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 2.5rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.value-item:hover {
  transform: translateX(10px);
}

.value-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(30deg, #ff7300, #10af9c);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 1.5rem;
  flex-shrink: 0;
}

.value-icon i {
  font-size: 1.5rem;
  color: white;
}

.value-text h5 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.value-text p {
  color: #6c757d;
  line-height: 1.6;
  margin: 0;
}

.values-image {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.values-decorative {
  position: relative;
  width: 300px;
  height: 300px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.values-pattern {
  width: 200px;
  height: 200px;
  background: linear-gradient(30deg, #ff7300, #10af9c);
  border-radius: 50%;
  opacity: 0.8;
  animation: pulse 4s ease-in-out infinite;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.values-text {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  z-index: 2;
  position: relative;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.9;
  }
}

/* CTA Section */
.cta-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #265e6d 0%, #10af9c 100%);
  color: white;
  overflow: hidden;
}

.cta-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("img/brain.png") repeat;
  background-size: 30px 30px;
  opacity: 0.12;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.cta-subtitle {
  font-size: 1.2rem;
  margin-bottom: 2.5rem;
  opacity: 0.9;
  line-height: 1.6;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  position: relative;
  z-index: 10;
}

.cta-buttons .btn {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  transition: all 0.3s ease;
  text-decoration: none;
  border: 2px solid transparent;
}

.cta-buttons .btn-primary {
  background: white;
  color: #265e6d;
  border-color: white;
}

.cta-buttons .btn-primary:hover {
  background: #ed7014;
  color: white;
  border-color: #ed7014;
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.cta-buttons .btn-outline-primary {
  background: transparent;
  color: white;
  border-color: white;
}

.cta-buttons .btn-outline-primary:hover {
  background: white;
  color: #265e6d;
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .hero-title {
    font-size: 3rem;
  }

  .section-title {
    font-size: 2.4rem;
  }
}

@media (max-width: 992px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .hero-stats {
    justify-content: center;
  }

  .section-title {
    font-size: 2.2rem;
  }

  .floating-card {
    padding: 2rem;
    margin-top: 3rem;
  }

  .values-content {
    margin-bottom: 3rem;
  }
}

@media (max-width: 768px) {
  .about-hero {
    padding: 80px 0 60px;
  }

  .hero-title {
    font-size: 2.2rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-stats {
    gap: 1rem;
  }

  .stat-item {
    padding: 1rem;
    flex: 1;
    min-width: 120px;
  }

  .stat-number {
    font-size: 2rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .section-subtitle {
    font-size: 1.1rem;
  }

  .mission-section,
  .team-section,
  .values-section,
  .cta-section {
    padding: 60px 0;
  }

  .mission-card,
  .team-card {
    margin-bottom: 2rem;
  }

  .floating-card {
    padding: 1.5rem;
  }

  .floating-card h4 {
    font-size: 1.2rem;
  }

  .value-item {
    flex-direction: column;
    text-align: center;
  }

  .value-icon {
    margin: 0 auto 1rem;
  }

  .cta-title {
    font-size: 2rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-buttons .btn {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 1.8rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .hero-stats {
    flex-direction: column;
    align-items: center;
  }

  .stat-item {
    width: 100%;
    max-width: 200px;
  }

  .mission-card,
  .team-content {
    padding: 1.5rem;
  }

  .team-image {
    height: 250px;
  }

  .floating-card {
    padding: 1rem;
  }

  .floating-card i {
    font-size: 2rem;
  }
}

/* Ensure Font Awesome icons display properly */
.fas,
.fab,
.far,
.fal,
.fad,
.fa {
  font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands",
    "Font Awesome 6 Pro" !important;
  font-weight: 900;
}

.fab {
  font-weight: 400;
}

/*---------- TRANSLATE ----------*/

.rtl {
  direction: rtl; /* Set the direction to right-to-left */
}
.ltr {
  direction: ltr; /* Set the direction to right-to-left */
}

.modal-header .close {
  padding: 1rem 1rem;
  margin: -1rem auto -1rem -1rem;
}

.custom-file-upload input[type="file"] {
  display: none;
}

/*---------- CARD UPDATE ----------*/

.profile-image-preview {
  width: 100px;
  height: 100px;
  overflow: hidden;
  border-radius: 50%;
  display: inline-block;
}
.profile-image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover; /* Crops the image to fit the square */
}

.time-slot-group {
  display: flex;
  gap: 5px;
  margin-bottom: 10px;
}

#scheduleContent ul,
#weekScheduleBody div {
  direction: ltr !important;
  text-align: right !important;
  list-style-type: none;
}

/* Credit Options */
.add {
  width: 2rem;
  height: 2rem;
  font-size: 1.5rem;
  background-color: #ffffff;
  color: #265e6d !important;
  display: flex;
  justify-content: center;
  align-items: center;
}
.add:hover {
  background-color: #10af9c;
  color: #ffffff !important;
}
.credit-options {
  display: flex;
  justify-content: space-around;
  gap: 5px;
}
.credit-card {
  flex: 1;
  display: inline-block;
  cursor: pointer;
  position: relative;
  text-align: center;
  background-color: transparent;
  border: 2px solid transparent;
  border-radius: 1rem;
}
.credit-card input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}
.credit-card input[type="radio"]:checked + .credit-card-content {
  border: 2px solid #ed7014;
  padding: 20px 10px;
  border-radius: 1rem;
  box-shadow: 0 4px 8px 0 rgba(220, 140, 30, 0.2),
    0 6px 20px 0 rgba(220, 140, 30, 0.19);
}
.credit-card-content {
  background-color: #ffffff;
  padding: 20px 10px;
  border-radius: 1rem;
}
.credit-card h3 {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 10px;
}
.credit-card h2 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #265e6d;
  margin-bottom: 15px;
}
.credit-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.credit-card ul li {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.5;
}

/* Recommended style */
.credit-card.recommended h2 {
  color: #ed7014;
}
/* Payment Methods */
.payment-methods {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-top: 20px;
}
.payment-method {
  cursor: pointer;
  text-align: center;
  border: 2px solid transparent;
  padding: 10px;
}
.payment-method input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}
.payment-method input[type="radio"]:checked + img {
  border: 2px solid #ed7014;
  box-shadow: 0 4px 8px 0 rgba(220, 140, 30, 0.2),
    0 6px 20px 0 rgba(220, 140, 30, 0.19);
}
.payment-method img {
  height: 40px;
  margin-bottom: 5px;
}
.payment-method span {
  font-weight: 600;
  color: #333;
}

/* Test Handler Styles */
.test-wrapper {
  margin-top: 100px;
  padding: 0 20px 20px 20px;
  height: 85vh;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
}
.test-handler-body {
  font-family: Rabar, "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  padding: 0;
  height: 100vh;
  background-color: #d5f6ff !important;
  overflow: hidden;
}
.test-container {
  width: 90%;
  min-width: 300px;
  max-width: 800px;
  background: #fff;
  padding: 0;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  position: relative;
  min-height: 600px;
}
.test-progress-bar {
  height: 8px;
  background: #e0e0e0;
  border-radius: 0;
  overflow: hidden;
}
.test-progress-bar .progress {
  height: 100%;
  background: linear-gradient(90deg, #ed7014, #ff8c00);
  width: 0%;
  transition: width 0.4s ease;
  border-radius: 0;
}
.testTitle {
  text-align: center;
  background: linear-gradient(135deg, #265e6d, #2e7d8a);
  color: white;
  padding: 20px;
  margin: 0;
  font-size: 1.5em;
  font-weight: 600;
}
.question-container {
  position: relative;
  width: 100%;
  height: 500px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}
.test-question {
  background: rgba(255, 255, 255, 0.95);
  position: absolute;
  padding: 20px 30px 30px 30px;
  width: calc(100% - 60px);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  transition: transform 0.5s ease, opacity 0.5s ease;
  color: #333;
  line-height: 1.6;
}
.test-question-text {
  font-size: 1.4em;
  text-align: center;
  margin: 0 0 30px 0;
  font-weight: 600;
  color: #265e6d;
  padding: 20px;
  background: rgba(237, 112, 20, 0.1);
  border-radius: 15px;
  border: 2px solid rgba(237, 112, 20, 0.2);
  width: 100%;
  box-sizing: border-box;
}
.test-options {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  gap: 15px;
  flex-wrap: wrap;
}
.test-options button {
  flex: 1;
  min-width: 120px;
  max-width: 150px;
  padding: 15px 10px;
  font-size: 0.9em;
  color: #555;
  background: rgba(237, 112, 20, 0.1);
  border: 2px solid rgba(237, 112, 20, 0.3);
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-family: Rabar, "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}
.test-options button:hover {
  background: #ed7014;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(237, 112, 20, 0.3);
}
.test-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  background: #f8f9fa;
  border-top: 1px solid #e0e0e0;
}
.test-navigation button {
  padding: 12px 25px;
  font-size: 1em;
  background: #265e6d;
  color: white;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}
.test-navigation button:hover:not(:disabled) {
  background: #2e7d8a;
  transform: translateY(-1px);
}
.test-navigation button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}
#slide {
  font-weight: 600;
  color: #666;
}
.test-result-container {
  text-align: center;
  padding: 40px 30px;
  color: #333;
}
.test-result-container h2 {
  color: #265e6d;
  margin-bottom: 20px;
}
.test-score-display {
  font-size: 2em;
  font-weight: bold;
  color: #ed7014;
  margin: 20px 0;
}
.test-scoring-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  margin: 20px 0;
  border-left: 4px solid #ed7014;
}

/* Test Handler Mobile Styles */
@media (max-width: 768px) {
  .test-options {
    gap: 10px;
  }
  .test-options button {
    flex: 1 1 calc(50% - 5px);
    min-width: calc(50% - 5px);
    max-width: calc(50% - 5px);
    font-size: 0.8em;
    padding: 12px 8px;
  }
  .test-wrapper {
    margin-top: 70px;
    height: 85vh;
  }
  .test-container {
    margin: 10px;
    min-height: 400px;
  }
  .test-question {
    font-size: 1.1em;
    padding: 15px 20px 20px 20px;
  }
  .test-question-text {
    font-size: 1.2em;
    padding: 15px;
    margin: 0 0 20px 0;
  }
  .test-navigation {
    padding: 10px 20px;
  }
  .test-navigation button {
    padding: 8px 16px;
    font-size: 0.9em;
  }
}
