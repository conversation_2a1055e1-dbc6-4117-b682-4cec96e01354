<?php
/**
 * Production Database Configuration Checker
 * Helps identify correct database credentials for production
 */

echo "<h1>🔍 Production Database Configuration Checker</h1>";

// Common database name patterns for cPanel hosting
$possibleDbNames = [
    'inspiremental',
    'yaridagr_inspiremental',
    'yaridagr_inspire',
    'yaridagr_mental',
    'yaridagr_main'
];

// Common username patterns
$possibleUsernames = [
    'yaridagr_inspiremental',
    'yaridagr_inspire',
    'yaridagr_mental',
    'yaridagr_admin',
    'yaridagr_user'
];

// The password from the error log
$password = 'n*dMFX=i0-iq';

echo "<h2>Testing Database Configurations:</h2>";

$successfulConfig = null;

foreach ($possibleUsernames as $username) {
    foreach ($possibleDbNames as $dbname) {
        echo "<h3>Testing: $username → $dbname</h3>";
        
        try {
            $con = new mysqli('localhost', $username, $password, $dbname);
            
            if ($con->connect_error) {
                echo "<p style='color: red;'>❌ Failed: " . $con->connect_error . "</p>";
            } else {
                echo "<p style='color: green;'>✅ SUCCESS! Connection established</p>";
                
                // Test if we can query the database
                $result = $con->query("SHOW TABLES");
                if ($result) {
                    $tableCount = $result->num_rows;
                    echo "<p style='color: blue;'>📊 Found $tableCount tables in database</p>";
                    
                    if ($tableCount > 0) {
                        echo "<h4>Tables found:</h4><ul>";
                        while ($row = $result->fetch_array()) {
                            echo "<li>" . $row[0] . "</li>";
                        }
                        echo "</ul>";
                    }
                } else {
                    echo "<p style='color: orange;'>⚠️ Connected but cannot show tables: " . $con->error . "</p>";
                }
                
                $successfulConfig = [
                    'username' => $username,
                    'dbname' => $dbname,
                    'password' => $password
                ];
                
                $con->close();
                break 2; // Exit both loops
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>";
        }
    }
}

if ($successfulConfig) {
    echo "<div style='background: #ccffcc; padding: 20px; border: 1px solid #00ff00; margin: 20px 0; border-radius: 5px;'>";
    echo "<h2>✅ Correct Configuration Found!</h2>";
    echo "<p><strong>Username:</strong> " . $successfulConfig['username'] . "</p>";
    echo "<p><strong>Database:</strong> " . $successfulConfig['dbname'] . "</p>";
    echo "<p><strong>Password:</strong> " . $successfulConfig['password'] . "</p>";
    echo "</div>";
    
    echo "<h3>🔧 Update db.php with this configuration:</h3>";
    echo "<pre style='background: #f0f0f0; padding: 10px; border-radius: 5px;'>";
    echo "'production' => [\n";
    echo "    'servername' => 'localhost',\n";
    echo "    'username' => '" . $successfulConfig['username'] . "',\n";
    echo "    'password' => '" . $successfulConfig['password'] . "',\n";
    echo "    'dbname' => '" . $successfulConfig['dbname'] . "'\n";
    echo "]";
    echo "</pre>";
    
} else {
    echo "<div style='background: #ffcccc; padding: 20px; border: 1px solid #ff0000; margin: 20px 0; border-radius: 5px;'>";
    echo "<h2>❌ No Working Configuration Found</h2>";
    echo "<p>None of the tested combinations worked. This could mean:</p>";
    echo "<ul>";
    echo "<li>The database credentials are incorrect</li>";
    echo "<li>The database doesn't exist yet</li>";
    echo "<li>The user doesn't have proper permissions</li>";
    echo "<li>The hosting configuration is different</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🔧 Next Steps:</h3>";
    echo "<ol>";
    echo "<li><strong>Check cPanel:</strong> Log into your hosting control panel</li>";
    echo "<li><strong>Database Section:</strong> Look for MySQL Databases or Database section</li>";
    echo "<li><strong>Find Database Name:</strong> Look for existing databases</li>";
    echo "<li><strong>Check User Permissions:</strong> Ensure the user has access to the database</li>";
    echo "<li><strong>Create Database:</strong> Create the database if it doesn't exist</li>";
    echo "</ol>";
}

// Additional diagnostic information
echo "<h2>🔍 Additional Diagnostics:</h2>";

echo "<h3>Server Information:</h3>";
echo "<p><strong>HTTP Host:</strong> " . ($_SERVER['HTTP_HOST'] ?? 'Not set') . "</p>";
echo "<p><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Not set') . "</p>";
echo "<p><strong>Script Path:</strong> " . (__FILE__ ?? 'Not set') . "</p>";

echo "<h3>PHP MySQL Support:</h3>";
if (extension_loaded('mysqli')) {
    echo "<p style='color: green;'>✅ MySQLi extension is loaded</p>";
} else {
    echo "<p style='color: red;'>❌ MySQLi extension is NOT loaded</p>";
}

echo "<h3>Common cPanel Database Patterns:</h3>";
echo "<p>Most cPanel hosting uses this pattern:</p>";
echo "<ul>";
echo "<li><strong>Database Name:</strong> cpanel_username_dbname</li>";
echo "<li><strong>Database User:</strong> cpanel_username_dbuser</li>";
echo "<li><strong>Example:</strong> yaridagr_inspiremental</li>";
echo "</ul>";

echo "<h3>🔗 Helpful Links:</h3>";
echo "<p><a href='https://yaridagr.com/cpanel' target='_blank'>cPanel Login</a> (if available)</p>";
echo "<p><a href='database-diagnostic.php'>Database Diagnostic Tool</a></p>";

echo "<div style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; margin: 20px 0;'>";
echo "<h3>⚠️ Security Warning</h3>";
echo "<p><strong>Delete this file after finding the correct configuration!</strong></p>";
echo "<p>This file contains sensitive database information.</p>";
echo "</div>";
?>
