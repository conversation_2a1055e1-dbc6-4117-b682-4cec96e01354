<?php
session_start();
include 'db.php';

$response = ["success" => false, "weekSchedule" => []];
$userId = $_SESSION['user_id']; // Ensure the session user_id is correctly set

// Debugging output to verify user_id
error_log("User ID: " . $userId);

// Array of English to Kurdish Sorani day translations
$daysOfWeekKurdish = [
    "Saturday" => "شەممە",
    "Sunday" => "یەک شەممە",
    "Monday" => "دوو شەممە",
    "Tuesday" => "سێ شەممە",
    "Wednesday" => "چوار شەممە",
    "Thursday" => "پێنج شەممە",
    "Friday" => "هەینی"
];

// Iterate through each day of the week
foreach (array_keys($daysOfWeekKurdish) as $day) {
    $stmt = $con->prepare("SELECT start_time, end_time FROM users_schedule WHERE user_id = ? AND day_of_week = ?");
    $stmt->bind_param("is", $userId, $day);
    $stmt->execute();
    $result = $stmt->get_result();

    $timeSlots = [];
    while ($row = $result->fetch_assoc()) {
        // Convert times to 12-hour format with AM/PM
        $row['start_time'] = date("g:i A", strtotime($row['start_time']));
        $row['end_time'] = date("g:i A", strtotime($row['end_time']));
        $timeSlots[] = $row;
    }

    // Translate the day of the week to Kurdish Sorani
    $dayInKurdish = $daysOfWeekKurdish[$day];

    // Add each day's schedule to the response, even if empty
    $response["weekSchedule"][] = [
        "day_of_week" => $dayInKurdish,
        "time_slots" => $timeSlots
    ];
}

// Set success to true if there’s at least one schedule entry
$response["success"] = array_filter($response["weekSchedule"], function($day) {
    return !empty($day["time_slots"]);
}) ? true : false;

// Output JSON response
header('Content-Type: application/json');
echo json_encode($response);
?>
