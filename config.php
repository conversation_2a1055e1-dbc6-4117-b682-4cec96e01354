<?php
/**
 * Global Configuration File
 * Auto-detects environment and sets appropriate configurations
 */

// Include database connection (which sets CURRENT_ENVIRONMENT and BASE_URL)
require_once 'db.php';

// Environment-specific configurations
$configs = [
    'localhost' => [
        'site_name' => 'ئیلهامبەخشی دەروونی - Development',
        'admin_url' => 'http://localhost/inspiremental/admin/',
        'api_url' => 'http://localhost/inspiremental/api/',
        'assets_url' => 'http://localhost/inspiremental/assets/',
        'uploads_url' => 'http://localhost/inspiremental/uploads/',
        'debug_mode' => true,
        'error_reporting' => true
    ],
    'production' => [
        'site_name' => 'ئیلهامبەخشی دەروونی',
        'admin_url' => 'https://inspiremental.org/admin/',
        'api_url' => 'https://inspiremental.org/api/',
        'assets_url' => 'https://inspiremental.org/assets/',
        'uploads_url' => 'https://inspiremental.org/uploads/',
        'debug_mode' => false,
        'error_reporting' => false
    ]
];

// Get current configuration
$currentConfig = $configs[CURRENT_ENVIRONMENT];

// Define global constants
define('SITE_NAME', $currentConfig['site_name']);
define('ADMIN_URL', $currentConfig['admin_url']);
define('API_URL', $currentConfig['api_url']);
define('ASSETS_URL', $currentConfig['assets_url']);
define('UPLOADS_URL', $currentConfig['uploads_url']);
define('DEBUG_MODE', $currentConfig['debug_mode']);

// Set error reporting based on environment
if ($currentConfig['error_reporting']) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

/**
 * Helper Functions
 */

// Generate URL for pages
function url($path = '') {
    return BASE_URL . ltrim($path, '/');
}

// Generate admin URL
function admin_url($path = '') {
    return ADMIN_URL . ltrim($path, '/');
}

// Generate API URL
function api_url($path = '') {
    return API_URL . ltrim($path, '/');
}

// Generate assets URL
function assets_url($path = '') {
    return ASSETS_URL . ltrim($path, '/');
}

// Generate uploads URL
function uploads_url($path = '') {
    return UPLOADS_URL . ltrim($path, '/');
}

// Check if we're in development mode
function is_development() {
    return CURRENT_ENVIRONMENT === 'localhost';
}

// Check if we're in production mode
function is_production() {
    return CURRENT_ENVIRONMENT === 'production';
}

// Debug function - only works in development
function debug($data, $label = '') {
    if (DEBUG_MODE) {
        echo '<div style="background: #f0f0f0; border: 1px solid #ccc; padding: 10px; margin: 10px 0; font-family: monospace;">';
        if ($label) {
            echo '<strong>' . htmlspecialchars($label) . ':</strong><br>';
        }
        echo '<pre>' . htmlspecialchars(print_r($data, true)) . '</pre>';
        echo '</div>';
    }
}
?>