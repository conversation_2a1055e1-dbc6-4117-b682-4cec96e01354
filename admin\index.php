<?php
session_start();
include '../db.php';

// Check if the user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header('Location: ../index.php');
    exit();
}

// Get statistics for dashboard
$users_count = $con->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
$tests_count = $con->query("SELECT COUNT(*) as count FROM tests")->fetch_assoc()['count'];
$transactions_count = $con->query("SELECT COUNT(*) as count FROM transactions")->fetch_assoc()['count'] ?? 0;
?>
<!DOCTYPE html>
<html lang="ckb" dir="rtl">
<head>
    <title>پانێڵی بەڕێوەبەر - ئیلهامبەخشی دەروونی</title>
    <?php include '../includes/head.php'; ?>
    <style>
        .admin-container {
            padding: 2rem 0;
            min-height: 100vh;
            background: #f8f9fa;
        }
        .admin-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .stat-card {
            background: linear-gradient(135deg, #265e6d, #10af9c);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 1rem;
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            display: block;
        }
        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        .admin-nav {
            background: white;
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 2rem;
        }
        .admin-nav a {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            margin: 0.25rem;
            background: #265e6d;
            color: white;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .admin-nav a:hover {
            background: #ed7014;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <?php include '../includes/admin_navbar.php'; ?>
    
    <div class="admin-container">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="admin-card">
                        <h1 class="text-center mb-4">پانێڵی بەڕێوەبەر</h1>
                        <p class="text-center text-muted">بەخێربێیت <?php echo $_SESSION['username']; ?></p>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="stat-card">
                        <span class="stat-number"><?php echo $users_count; ?></span>
                        <span class="stat-label">بەکارهێنەر</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card">
                        <span class="stat-number"><?php echo $tests_count; ?></span>
                        <span class="stat-label">تێست</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card">
                        <span class="stat-number"><?php echo $transactions_count; ?></span>
                        <span class="stat-label">مامەڵە</span>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-12">
                    <div class="admin-nav text-center">
                        <h3 class="mb-3">بەڕێوەبردنی سیستەم</h3>
                        <a href="users.php">بەڕێوەبردنی بەکارهێنەران</a>
                        <a href="tests.php">بەڕێوەبردنی تێستەکان</a>
                        <a href="transactions.php">بەڕێوەبردنی مامەڵەکان</a>
                        <a href="../index.php">گەڕانەوە بۆ سایت</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <?php include '../includes/script.php'; ?>
</body>
</html>
