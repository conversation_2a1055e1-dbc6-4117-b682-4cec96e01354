RewriteEngine On

# Production configuration for inspiremental.org only

# Force HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Redirect base URL to home page
RewriteRule ^$ home [R=301,L]

# Redirect old index.php URLs to clean URLs
RewriteCond %{THE_REQUEST} \s/+[^/\s]*\.php\?page=([^\s&]+) [NC]
RewriteRule ^ %1? [R=301,L]

# Fix for production server path issues - redirect to clean URLs
RewriteCond %{REQUEST_URI} ^/home4/yaridagr/public_html/inspiremental/(.*)$ [NC]
RewriteRule ^.*$ https://inspiremental.org/$1 [R=301,L]

# Alternative fix for any server path issues
RewriteCond %{REQUEST_URI} ^/.*/public_html/inspiremental/(.*)$ [NC]
RewriteRule ^.*$ https://inspiremental.org/$1 [R=301,L]

# Admin panel routing
RewriteRule ^admin/?$ admin/index.php [L]
RewriteRule ^admin/([^/]+)/?$ admin/$1.php [L]

# API routing
RewriteRule ^api/([^/]+)/?$ api/$1.php [L]

# Main page routing - Universal approach
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^.*/admin/ [NC]
RewriteCond %{REQUEST_URI} !^.*/api/ [NC]
RewriteCond %{REQUEST_URI} !^.*/assets/ [NC]
RewriteCond %{REQUEST_URI} !^.*/uploads/ [NC]
RewriteCond %{REQUEST_URI} !^.*/img/ [NC]
RewriteCond %{REQUEST_URI} !^.*/font/ [NC]
RewriteCond %{REQUEST_URI} !^.*/guide/ [NC]
RewriteCond %{REQUEST_URI} !^.*\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ [NC]
RewriteRule ^([^/]+)/?$ index.php?page=$1 [L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Prevent access to sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

<Files ~ "\.sql$">
    Order allow,deny
    Deny from all
</Files>
