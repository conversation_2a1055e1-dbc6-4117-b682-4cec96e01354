RewriteEngine On
RewriteBase /inspiremental/
# RewriteBase https://inspire.mental.mtag96.site/

# Redirect base URL to /home
RewriteRule ^$ /inspiremental/home [R=301,L]
# RewriteRule ^$ https://inspire.mental.mtag96.site/home [R=301,L]

# Redirect from /index.php?page=home to /home
RewriteCond %{THE_REQUEST} \s/index\.php\?page=home\s [NC]
RewriteRule ^ /home? [R=301,L]

# Redirect from /index.php?page=about to /about
RewriteCond %{THE_REQUEST} \s/index\.php\?page=([^\s&]+)\s [NC]
RewriteRule ^ /%1? [R=301,L]

# Rewrite /home to index.php?page=home
RewriteRule ^home$ index.php?page=home [L]

# Admin panel routing
RewriteRule ^admin/?$ admin/index.php [L]
RewriteRule ^admin/([^/]+)/?$ admin/$1.php [L]

# API routing
RewriteRule ^api/([^/]+)/?$ api/$1.php [L]

# Rewrite other pages to their clean URL format
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/inspiremental/admin/
RewriteCond %{REQUEST_URI} !^/inspiremental/api/
RewriteCond %{REQUEST_URI} !^/inspiremental/assets/
RewriteCond %{REQUEST_URI} !^/inspiremental/uploads/
RewriteRule ^([^/]+)$ index.php?page=$1 [L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Prevent access to sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

<Files ~ "\.sql$">
    Order allow,deny
    Deny from all
</Files>
