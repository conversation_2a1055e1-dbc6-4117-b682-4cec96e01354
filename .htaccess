RewriteEngine On

# Configuration for localhost
RewriteBase /inspiremental/

# Redirect base URL to home page
RewriteRule ^$ home [R=301,L]

# Redirect old index.php URLs to clean URLs
RewriteCond %{THE_REQUEST} \s/+[^/\s]*\.php\?page=([^\s&]+) [NC]
RewriteRule ^ %1? [R=301,L]

# Admin panel routing
RewriteRule ^admin/?$ admin/index.php [L]
RewriteRule ^admin/([^/]+)/?$ admin/$1.php [L]

# API routing  
RewriteRule ^api/([^/]+)/?$ api/$1.php [L]

# Main page routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/inspiremental/admin/
RewriteCond %{REQUEST_URI} !^/inspiremental/api/
RewriteCond %{REQUEST_URI} !^/inspiremental/assets/
RewriteCond %{REQUEST_URI} !^/inspiremental/uploads/
RewriteCond %{REQUEST_URI} !^/inspiremental/img/
RewriteCond %{REQUEST_URI} !^/inspiremental/font/
RewriteCond %{REQUEST_URI} !^/inspiremental/guide/
RewriteRule ^([^/]+)/?$ index.php?page=$1 [L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Prevent access to sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

<Files ~ "\.sql$">
    Order allow,deny
    Deny from all
</Files>
