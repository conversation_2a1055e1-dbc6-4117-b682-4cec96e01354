RewriteEngine On

# Environment detection and RewriteBase setting
# For localhost (XAMPP/WAMP): /inspiremental/
# For production: /

# Localhost configuration
RewriteCond %{HTTP_HOST} ^(localhost|127\.0\.0\.1)(:[0-9]+)?$ [NC]
RewriteBase /inspiremental/

# Production configuration
RewriteCond %{HTTP_HOST} !^(localhost|127\.0\.0\.1)(:[0-9]+)?$ [NC]
RewriteBase /

# Redirect base URL to home page
RewriteRule ^$ home [R=301,L]

# Redirect old index.php URLs to clean URLs
RewriteCond %{THE_REQUEST} \s/+[^/\s]*\.php\?page=([^\s&]+) [NC]
RewriteRule ^ %1? [R=301,L]

# Admin panel routing
RewriteRule ^admin/?$ admin/index.php [L]
RewriteRule ^admin/([^/]+)/?$ admin/$1.php [L]

# API routing
RewriteRule ^api/([^/]+)/?$ api/$1.php [L]

# Main page routing with environment-aware exclusions
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Exclude directories from rewriting (works for both environments)
RewriteCond %{REQUEST_URI} !/(admin|api|assets|uploads|img|font|guide)/ [NC]
RewriteRule ^([^/]+)/?$ index.php?page=$1 [L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Prevent access to sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

<Files ~ "\.sql$">
    Order allow,deny
    Deny from all
</Files>
