<?php
/**
 * API Debug
 * Check what's causing the 500 error
 */

// Enable error display for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 API Debug</h1>";

echo "<h2>1. PHP Info:</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Current Directory:</strong> " . getcwd() . "</p>";
echo "<p><strong>Script Path:</strong> " . __FILE__ . "</p>";

echo "<h2>2. File System Check:</h2>";
$files_to_check = [
    '../db.php',
    'test-handler.php',
    '../index.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ $file exists</p>";
    } else {
        echo "<p style='color: red;'>❌ $file not found</p>";
    }
}

echo "<h2>3. Database Test:</h2>";
try {
    $pdo = new PDO("mysql:host=localhost;dbname=yaridagr_inspiremental;charset=utf8mb4", 
                   "yaridagr_inspiremental", "n*dMFX=i0-iq");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ Database connection successful (yaridagr_inspiremental)</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM tests");
    $count = $stmt->fetch()['count'];
    echo "<p style='color: green;'>✅ Tests table accessible with $count tests</p>";
    
} catch(PDOException $e) {
    echo "<p style='color: red;'>❌ Database yaridagr_inspiremental failed: " . $e->getMessage() . "</p>";
    
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=inspiremental;charset=utf8mb4", 
                       "yaridagr_inspiremental", "n*dMFX=i0-iq");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<p style='color: green;'>✅ Alternative database connection successful (inspiremental)</p>";
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM tests");
        $count = $stmt->fetch()['count'];
        echo "<p style='color: green;'>✅ Tests table accessible with $count tests</p>";
        
    } catch(PDOException $e2) {
        echo "<p style='color: red;'>❌ Alternative database also failed: " . $e2->getMessage() . "</p>";
    }
}

echo "<h2>4. Include Test:</h2>";
try {
    $db_path = dirname(__FILE__) . '/../db.php';
    echo "<p><strong>DB Path:</strong> $db_path</p>";
    
    if (file_exists($db_path)) {
        echo "<p style='color: green;'>✅ db.php file exists at expected path</p>";
        
        // Try to include it
        ob_start();
        include_once $db_path;
        $output = ob_get_clean();
        
        if (!empty($output)) {
            echo "<p style='color: orange;'>⚠️ db.php produced output: " . htmlspecialchars($output) . "</p>";
        } else {
            echo "<p style='color: green;'>✅ db.php included successfully</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ db.php not found at expected path</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Include error: " . $e->getMessage() . "</p>";
}

echo "<h2>5. Test Links:</h2>";
echo "<p><a href='simple-handler?test_id=1' target='_blank'>🧪 Simple Handler</a></p>";
echo "<p><a href='test-handler?test_id=1' target='_blank'>🧪 Original Handler</a></p>";

echo "<div style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; margin: 20px 0;'>";
echo "<h3>⚠️ Note</h3>";
echo "<p>Delete this debug file after testing!</p>";
echo "</div>";
?>
