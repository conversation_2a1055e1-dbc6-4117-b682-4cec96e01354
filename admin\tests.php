<?php
session_start();
include '../db.php';

// Check if the user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'Admin') {
    header('Location: ../index.php');
    exit();
}

// Fetch tests from the database with user information
$result = $con->query("
    SELECT t.id, t.user_id, t.test_type, t.score, t.result, t.created_at, u.username 
    FROM tests t 
    LEFT JOIN users u ON t.user_id = u.id 
    ORDER BY t.created_at DESC
");
?>
<!DOCTYPE html>
<html lang="ckb" dir="rtl">
<head>
    <title>بەڕێوەبردنی تاقیکردنەوەکان - ئیلهامبەخشی دەروونی</title>
    <?php include '../includes/head.php'; ?>
    <style>
        @font-face {
            font-family: Rabar;
            src: url(../font/Rabar_22.ttf);
        }
        body {
            font-family: <PERSON>bar, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            padding-top: 0;
        }
        .admin-container {
            padding: 2rem 0;
            min-height: 100vh;
        }
        .admin-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .table {
            font-family: Rabar;
        }
        .table th {
            background: linear-gradient(135deg, #265e6d, #10af9c);
            color: white;
            border: none;
            font-weight: 600;
        }
        .table td {
            vertical-align: middle;
            border-color: #e9ecef;
        }
        .score-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .score-low { background: #28a745; color: white; }
        .score-medium { background: #ffc107; color: #212529; }
        .score-high { background: #dc3545; color: white; }
        .test-type-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            background: #17a2b8;
            color: white;
        }
    </style>
</head>
<body>
    <?php include '../includes/admin_navbar.php'; ?>
    
    <div class="admin-container">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="admin-card">
                        <h2 class="mb-4">بەڕێوەبردنی تاقیکردنەوەکان</h2>
                        
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>بەکارهێنەر</th>
                                        <th>جۆری تێست</th>
                                        <th>خاڵ</th>
                                        <th>ئەنجام</th>
                                        <th>بەروار</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($test = $result->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($test['username'] ?? 'نەناسراو'); ?></td>
                                        <td>
                                            <span class="test-type-badge">
                                                <?php 
                                                $test_types = [
                                                    'depression' => 'خەمۆکی',
                                                    'anxiety' => 'دڵەڕاوکێ',
                                                    'stress' => 'فشار',
                                                    'personality' => 'کەسایەتی'
                                                ];
                                                echo $test_types[$test['test_type']] ?? $test['test_type'];
                                                ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php 
                                            $score = (int)$test['score'];
                                            $badge_class = 'score-low';
                                            if ($score >= 15) $badge_class = 'score-high';
                                            elseif ($score >= 10) $badge_class = 'score-medium';
                                            ?>
                                            <span class="score-badge <?php echo $badge_class; ?>">
                                                <?php echo $score; ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($test['result']); ?></td>
                                        <td><?php echo date('Y/m/d H:i', strtotime($test['created_at'])); ?></td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <?php include '../includes/script.php'; ?>
</body>
</html>
