# 🚀 Deployment Guide - ئیلهامبەخشی دەروونی

## ✨ **NEW: Automatic Environment Detection**

The website now **automatically detects** whether it's running on localhost or production and configures itself accordingly. **No manual configuration needed!**

### 🔄 **How It Works:**

- **Localhost**: Automatically detected and uses local database credentials
- **Production**: Automatically detected and uses production database credentials
- **URLs**: Automatically generates correct URLs for each environment
- **Assets**: Automatically loads CSS/JS from correct paths

## 📋 Quick Setup (Optional)

### For Localhost (XAMPP/WAMP)

```bash
php deploy.php localhost
```

### For Production (inspiremental.org)

```bash
php deploy.php production
```

**Note:** The deploy script is now optional since the website auto-detects the environment!

## 🤖 **Automatic Environment Detection**

### **Detection Logic:**

The system automatically detects the environment based on:

- **Server hostname** (localhost, 127.0.0.1, inspiremental.org)
- **Server IP address** (127.0.0.1, ::1 for localhost)
- **Domain patterns** (.local, localhost, inspiremental.org)

### **Database Credentials:**

- **Localhost**: `root` / `(no password)` / `inspiremental`
- **Production**: `yaridagr_inspiremental` / `n*dMFX=i0-iq` / `yaridagr_inspiremental`

### **URL Generation:**

- **Localhost**: `http://localhost/inspiremental/`
- **Production**: `https://inspiremental.org/`

### **Testing Environment Detection:**

Visit: `http://localhost/inspiremental/test-environment.php` to see current environment settings.

## 🌐 URL Structure

### Localhost URLs

- **Home**: `http://localhost/inspiremental/home`
- **About**: `http://localhost/inspiremental/about`
- **Online Test**: `http://localhost/inspiremental/online-test`
- **Chat**: `http://localhost/inspiremental/talk-to-me`
- **Admin Panel**: `http://localhost/inspiremental/admin`
- **API**: `http://localhost/inspiremental/api/endpoint`

### Production URLs

- **Home**: `https://inspiremental.org/home`
- **About**: `https://inspiremental.org/about`
- **Online Test**: `https://inspiremental.org/online-test`
- **Chat**: `https://inspiremental.org/talk-to-me`
- **Admin Panel**: `https://inspiremental.org/admin`
- **API**: `https://inspiremental.org/api/endpoint`

## 🔧 Manual Configuration

### 1. Localhost Setup

If you can't run the deploy script, manually update these files:

#### `.htaccess`

```apache
RewriteEngine On
RewriteBase /inspiremental/

# Redirect base URL to home page
RewriteRule ^$ home [R=301,L]

# Admin panel routing
RewriteRule ^admin/?$ admin/index.php [L]
RewriteRule ^admin/([^/]+)/?$ admin/$1.php [L]

# API routing
RewriteRule ^api/([^/]+)/?$ api/$1.php [L]

# Main page routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/inspiremental/admin/
RewriteCond %{REQUEST_URI} !^/inspiremental/api/
RewriteCond %{REQUEST_URI} !^/inspiremental/assets/
RewriteCond %{REQUEST_URI} !^/inspiremental/uploads/
RewriteRule ^([^/]+)/?$ index.php?page=$1 [L]
```

#### `db.php`

```php
<?php
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "inspiremental";

$con = new mysqli($servername, $username, $password, $dbname);
if ($con->connect_error) {
    die("Connection failed: " . $con->connect_error);
}
$con->set_charset("utf8mb4");
?>
```

### 2. Production Setup

#### `.htaccess`

```apache
RewriteEngine On
RewriteBase /

# Redirect base URL to home page
RewriteRule ^$ home [R=301,L]

# Admin panel routing
RewriteRule ^admin/?$ admin/index.php [L]
RewriteRule ^admin/([^/]+)/?$ admin/$1.php [L]

# API routing
RewriteRule ^api/([^/]+)/?$ api/$1.php [L]

# Main page routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/admin/
RewriteCond %{REQUEST_URI} !^/api/
RewriteCond %{REQUEST_URI} !^/assets/
RewriteCond %{REQUEST_URI} !^/uploads/
RewriteRule ^([^/]+)/?$ index.php?page=$1 [L]
```

#### `db.php`

```php
<?php
$servername = "localhost";
$username = "yaridagr_inspiremental";
$password = ".y$4Tx}25x7&";
$dbname = "yaridagr_inspiremental";

$con = new mysqli($servername, $username, $password, $dbname);
if ($con->connect_error) {
    die("Connection failed: " . $con->connect_error);
}
$con->set_charset("utf8mb4");
?>
```

## 📁 File Structure

```
inspiremental/
├── admin/              # Admin panel
├── api/                # API endpoints
├── assets/             # CSS, JS, fonts
├── includes/           # Common includes
├── pages/              # Main pages
├── img/                # Images
├── uploads/            # User uploads
├── deploy.php          # Deployment script
├── config.php          # Environment config
├── .htaccess           # URL rewriting
└── db.php              # Database connection
```

## 🔒 Security Notes

### Production Checklist

- [ ] Update database credentials
- [ ] Enable HTTPS
- [ ] Set proper file permissions
- [ ] Remove debug files
- [ ] Enable error logging (disable display)
- [ ] Configure backup system

### Files to Secure

- `db.php` - Database credentials
- `config.php` - Environment settings
- `deploy.php` - Deployment script
- `.sql` files - Database dumps

## 🚨 Troubleshooting

### Common Issues

#### 1. 404 Errors

- Check `.htaccess` RewriteBase setting
- Verify mod_rewrite is enabled
- Check file permissions

#### 2. Database Connection Errors

- Verify database credentials in `db.php`
- Check database server status
- Ensure database exists

#### 3. Admin Panel 500 Errors

- Check PHP error logs
- Verify user permissions
- Check database table existence

#### 4. Asset Loading Issues

- Check file paths in CSS/JS
- Verify asset directory structure
- Check web server configuration

## 📞 Support

For technical support or deployment issues:

- Check error logs first
- Verify configuration files
- Test on localhost before production
- Contact development team if needed

## 🔄 Switching Environments

### From Localhost to Production

```bash
php deploy.php production
```

### From Production to Localhost

```bash
php deploy.php localhost
```

### Verify Configuration

After deployment, test these URLs:

- Home page loads correctly
- Admin panel redirects properly
- API endpoints respond
- Assets load without errors
