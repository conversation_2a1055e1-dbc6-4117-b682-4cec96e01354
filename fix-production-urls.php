<?php
/**
 * Production URL Fix Script
 * Run this script on the production server to fix URL issues
 */

echo "<h1>🔧 Production URL Fix Script</h1>";

// Check current environment
include 'db.php';

echo "<h2>Current Environment Detection:</h2>";
echo "<p><strong>Environment:</strong> " . CURRENT_ENVIRONMENT . "</p>";
echo "<p><strong>Base URL:</strong> " . BASE_URL . "</p>";

// Server information
echo "<h2>Server Information:</h2>";
echo "<p><strong>HTTP Host:</strong> " . ($_SERVER['HTTP_HOST'] ?? 'Not set') . "</p>";
echo "<p><strong>Server Name:</strong> " . ($_SERVER['SERVER_NAME'] ?? 'Not set') . "</p>";
echo "<p><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Not set') . "</p>";
echo "<p><strong>Script Name:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "</p>";
echo "<p><strong>Request URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "</p>";

// Check for problematic URLs
$currentUrl = "https://" . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
echo "<p><strong>Current URL:</strong> " . $currentUrl . "</p>";

if (strpos($currentUrl, 'public_html') !== false || strpos($currentUrl, 'home4') !== false) {
    echo "<div style='background: #ffcccc; padding: 10px; border: 1px solid #ff0000; margin: 10px 0;'>";
    echo "<h3>❌ URL Problem Detected!</h3>";
    echo "<p>The URL contains server path information that should not be visible.</p>";
    echo "<p><strong>Problematic URL:</strong> " . $currentUrl . "</p>";
    echo "</div>";
    
    // Provide solutions
    echo "<h2>🔧 Solutions:</h2>";
    
    echo "<h3>Solution 1: Force Production Mode</h3>";
    echo "<ol>";
    echo "<li>Create an empty file named <code>force-production.flag</code> in the root directory</li>";
    echo "<li>Add this line to the top of <code>db.php</code>: <code>include_once 'production-config.php';</code></li>";
    echo "<li>The website will use production settings</li>";
    echo "</ol>";
    
    echo "<h3>Solution 2: Update .htaccess</h3>";
    echo "<p>The current .htaccess should redirect these URLs automatically. If it's not working:</p>";
    echo "<ol>";
    echo "<li>Check if mod_rewrite is enabled on your server</li>";
    echo "<li>Verify .htaccess file permissions (should be 644)</li>";
    echo "<li>Contact your hosting provider if the issue persists</li>";
    echo "</ol>";
    
    echo "<h3>Solution 3: Manual Redirect Test</h3>";
    echo "<p><a href='https://inspiremental.org/home' style='background: #007cba; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Test Direct URL</a></p>";
    
} else {
    echo "<div style='background: #ccffcc; padding: 10px; border: 1px solid #00ff00; margin: 10px 0;'>";
    echo "<h3>✅ URLs Look Good!</h3>";
    echo "<p>No problematic server paths detected in the URL.</p>";
    echo "</div>";
}

// Test database connection
echo "<h2>Database Connection Test:</h2>";
global $con;
if ($con && !$con->connect_error) {
    echo "<p style='color: green;'>✅ Database connected successfully!</p>";
    
    // Test a simple query
    $result = $con->query("SELECT COUNT(*) as count FROM users");
    if ($result) {
        $count = $result->fetch_assoc()['count'];
        echo "<p style='color: blue;'>📊 Users in database: " . $count . "</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Could not query users table: " . $con->error . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Database connection failed!</p>";
    if ($con) {
        echo "<p style='color: red;'>Error: " . $con->connect_error . "</p>";
    }
}

// Quick links for testing
echo "<h2>🔗 Test Links:</h2>";
echo "<p><a href='home'>Home Page</a></p>";
echo "<p><a href='about'>About Page</a></p>";
echo "<p><a href='admin'>Admin Panel</a></p>";
echo "<p><a href='online-test'>Online Test</a></p>";

// Instructions
echo "<h2>📝 Next Steps:</h2>";
echo "<ol>";
echo "<li><strong>If URLs are working correctly:</strong> Delete this file for security</li>";
echo "<li><strong>If URLs have problems:</strong> Follow the solutions above</li>";
echo "<li><strong>For persistent issues:</strong> Contact your hosting provider about mod_rewrite and .htaccess support</li>";
echo "</ol>";

echo "<div style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; margin: 20px 0;'>";
echo "<h3>⚠️ Security Warning</h3>";
echo "<p><strong>Delete this file (fix-production-urls.php) after testing!</strong></p>";
echo "<p>This file contains sensitive server information and should not be accessible on a live website.</p>";
echo "</div>";
?>
