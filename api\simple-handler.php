<?php
/**
 * Simple Test Handler
 * Minimal version to test basic functionality
 */

// Set content type
header('Content-Type: text/html; charset=utf-8');

// Simple error handling
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors in production
ini_set('log_errors', 1);

echo "<h1>🧪 Simple Test Handler</h1>";

// Get test ID
$test_id = isset($_GET['test_id']) ? (int)$_GET['test_id'] : 1;
echo "<p><strong>Test ID:</strong> $test_id</p>";

// Direct database connection
try {
    $servername = "localhost";
    $username = "yaridagr_inspiremental";
    $password = "n*dMFX=i0-iq";
    $dbname = "yaridagr_inspiremental";
    
    $pdo = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    
    // Fetch test data
    $stmt = $pdo->prepare("SELECT * FROM tests WHERE id = ?");
    $stmt->execute([$test_id]);
    $test = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$test) {
        echo "<p style='color: red;'>❌ Test not found for ID: $test_id</p>";
        
        // Show available tests
        $stmt = $pdo->query("SELECT id, title FROM tests ORDER BY id");
        $tests = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Available Tests:</h3>";
        echo "<ul>";
        foreach ($tests as $t) {
            echo "<li><a href='?test_id=" . $t['id'] . "'>" . htmlspecialchars($t['title']) . " (ID: " . $t['id'] . ")</a></li>";
        }
        echo "</ul>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ Test found: " . htmlspecialchars($test['title']) . "</p>";
    
    // Parse questions
    $questions = json_decode($test['questions'], true);
    if (!$questions) {
        echo "<p style='color: red;'>❌ Invalid test data</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ Questions parsed: " . count($questions) . " questions</p>";
    
    // Display test
    echo "<div style='max-width: 800px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 10px;'>";
    echo "<h2>" . htmlspecialchars($test['title']) . "</h2>";
    
    if ($test['description']) {
        echo "<p>" . htmlspecialchars($test['description']) . "</p>";
    }
    
    echo "<form id='testForm' method='post'>";
    
    foreach ($questions as $index => $question) {
        echo "<div style='margin: 20px 0; padding: 15px; background: #f9f9f9; border-radius: 5px;'>";
        echo "<h4>پرسیار " . ($index + 1) . ": " . htmlspecialchars($question['question']) . "</h4>";
        
        foreach ($question['options'] as $optIndex => $option) {
            $score = $question['scores'][$optIndex] ?? 0;
            echo "<label style='display: block; margin: 5px 0;'>";
            echo "<input type='radio' name='q" . $index . "' value='$score' required> ";
            echo htmlspecialchars($option);
            echo "</label>";
        }
        echo "</div>";
    }
    
    echo "<button type='submit' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>ناردنی وەڵام</button>";
    echo "</form>";
    echo "</div>";
    
    // Handle form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $totalScore = 0;
        $answeredQuestions = 0;
        
        foreach ($questions as $index => $question) {
            if (isset($_POST['q' . $index])) {
                $totalScore += (int)$_POST['q' . $index];
                $answeredQuestions++;
            }
        }
        
        echo "<div style='max-width: 800px; margin: 20px auto; padding: 20px; background: #e7f3ff; border: 1px solid #007bff; border-radius: 10px;'>";
        echo "<h3>ئەنجامی تێست</h3>";
        echo "<p><strong>کۆی خاڵەکان:</strong> $totalScore</p>";
        echo "<p><strong>ژمارەی پرسیارەکان:</strong> $answeredQuestions</p>";
        
        // Simple scoring
        $percentage = ($totalScore / ($answeredQuestions * 5)) * 100;
        
        if ($percentage >= 80) {
            echo "<p style='color: green;'><strong>ئەنجام:</strong> زۆر باش</p>";
        } elseif ($percentage >= 60) {
            echo "<p style='color: blue;'><strong>ئەنجام:</strong> باش</p>";
        } elseif ($percentage >= 40) {
            echo "<p style='color: orange;'><strong>ئەنجام:</strong> ناوەند</p>";
        } else {
            echo "<p style='color: red;'><strong>ئەنجام:</strong> پێویستت بە یارمەتی هەیە</p>";
        }
        
        echo "</div>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
    
    // Try alternative database name
    try {
        $dbname = "inspiremental";
        $pdo = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<p style='color: green;'>✅ Connected to alternative database: $dbname</p>";
        
        // Redirect to same handler with working database
        echo "<p><a href='?test_id=$test_id'>Try again with working database</a></p>";
        
    } catch (PDOException $e2) {
        echo "<p style='color: red;'>❌ Alternative database also failed: " . $e2->getMessage() . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ General error: " . $e->getMessage() . "</p>";
}

echo "<div style='margin: 20px 0;'>";
echo "<p><a href='../online-test'>← بگەڕێوە بۆ لیستی تێستەکان</a></p>";
echo "</div>";
?>
